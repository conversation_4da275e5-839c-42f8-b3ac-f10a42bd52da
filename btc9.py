# BTC Trading Bot PROFESIONAL v3.0 - VERSIÓN ULTRA MEJORADA
# Sistema completo con ML avanzado, features expandidos y detección TP/SL robusta
# Diseñado para ejecutar trades reales en modo simulación con máxima efectividad

import ccxt
import pandas as pd
import numpy as np
import talib
import time
import datetime
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.utils.class_weight import compute_class_weight
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix
from xgboost import XGBClassifier
import lightgbm as lgb
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import warnings
from scipy import stats
warnings.filterwarnings('ignore')

# Configuración DEMO ACTIVA - VERSIÓN MEJORADA
LOOKBACK = 30  # Incrementado para mejor contexto histórico
SYMBOL = 'BTC/USDT'  # Cambiar aquí para otro activo: 'ETH/USDT', 'SOL/USDT', etc.
INITIAL_CAPITAL = 10000
MAX_RISK_PER_TRADE = 0.02  # 2% por trade
MAX_DAILY_LOSS = 0.05  # 5% pérdida máxima diaria
MIN_WIN_RATE = 0.40  # 40% mínimo
MIN_PROFIT_FACTOR = 1.0
MIN_CONFIDENCE = 0.55  # Incrementado a 55% para mayor selectividad
MAX_POSITIONS = 1  # Solo una posición por vez

# NUEVOS PARÁMETROS AVANZADOS
TP_SL_TOLERANCE = 0.0005  # 0.05% tolerancia para TP/SL
TRAILING_STOP_ENABLED = True  # Trailing stop automático
ATR_MULTIPLIER_SL = 1.8  # Multiplicador ATR para stop loss (más conservador)
ATR_MULTIPLIER_TP = 2.5  # Multiplicador ATR para take profit
ENSEMBLE_MIN_AGREEMENT = 0.6  # 60% acuerdo mínimo entre modelos
VOLATILITY_FILTER = True  # Filtrar mercados de alta volatilidad
MAX_VOLATILITY_THRESHOLD = 0.05  # 5% volatilidad máxima
FEATURE_SELECTION_ENABLED = True  # Selección automática de features

# Crear directorios
for dir_name in ['modelos', 'logs', 'trades', 'reports']:
    os.makedirs(dir_name, exist_ok=True)

class TradingLogger:
    """Sistema de logging mejorado"""
    def __init__(self):
        self.log_file = f"logs/trading_{datetime.datetime.now().strftime('%Y%m%d')}.log"

    def log(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"{timestamp} [{level}] {message}"
        print(formatted_message)

        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(formatted_message + "\n")
        except:
            pass

logger = TradingLogger()

class DataManager:
    """Gestión profesional de datos"""
    def __init__(self):
        self.exchange = None

    def get_live_data(self, symbol=SYMBOL, timeframe='15m', limit=500):
        """Obtiene datos en vivo de Binance"""
        try:
            if not self.exchange:
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'options': {'defaultType': 'spot'}
                })

            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            logger.log(f"✅ Datos obtenidos: {len(df)} velas de {timeframe}")
            return df

        except Exception as e:
            logger.log(f"❌ Error obteniendo datos: {str(e)}", "ERROR")
            return self.get_mock_data(timeframe, limit)

    def get_mock_data(self, timeframe='15m', limit=500):
        """Datos simulados realistas para testing"""
        np.random.seed(42)
        freq_map = {'5m': '5T', '15m': '15T', '1h': '1H', '2h': '2H', '4h': '4H'}

        timestamps = pd.date_range(end=pd.Timestamp.now(), periods=limit, freq=freq_map[timeframe])

        # Simulación más realista con tendencias y volatilidad variable
        # Ajustar base_price según el activo
        base_prices = {
            'BTC/USDT': 98000,
            'ETH/USDT': 3500,
            'SOL/USDT': 140,
            'BNB/USDT': 600
        }
        base_price = base_prices.get(SYMBOL, 98000)

        drift = 0.0001
        volatility = 0.001

        prices = [base_price]
        for i in range(1, limit):
            change = drift + np.random.normal(0, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        close = np.array(prices)
        open_prices = np.roll(close, 1)
        open_prices[0] = base_price

        high = np.maximum(close, open_prices) * (1 + np.random.uniform(0, 0.002, limit))
        low = np.minimum(close, open_prices) * (1 - np.random.uniform(0, 0.002, limit))
        volume = np.random.lognormal(10, 0.5, limit) * 1000

        df = pd.DataFrame({
            'open': open_prices,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=timestamps)

        logger.log(f"✅ Datos simulados generados: {len(df)} velas")
        return df

class FeatureEngineer:
    """Feature Engineering avanzado y optimizado"""

    @staticmethod
    def add_price_features(df):
        """Features básicos de precio mejorados"""
        # Returns múltiples períodos
        df['returns'] = df['close'].pct_change()
        df['returns_2'] = df['close'].pct_change(2)
        df['returns_5'] = df['close'].pct_change(5)
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # Volatility expandida
        df['volatility'] = df['returns'].rolling(20).std()
        df['volatility_5'] = df['returns'].rolling(5).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(50).mean()
        df['volatility_rank'] = df['volatility'].rolling(100).rank(pct=True)

        # Price ratios mejorados
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['co_ratio'] = (df['close'] - df['open']) / df['open']
        df['oc_ratio'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)

        # Momentum features
        df['price_momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['price_momentum_10'] = df['close'] / df['close'].shift(10) - 1
        df['price_acceleration'] = df['returns'] - df['returns'].shift(1)

        return df

    @staticmethod
    def add_technical_indicators(df):
        """Indicadores técnicos avanzados y optimizados"""
        # RSI múltiples períodos
        df['rsi_7'] = talib.RSI(df['close'], timeperiod=7)
        df['rsi_14'] = talib.RSI(df['close'], timeperiod=14)
        df['rsi_21'] = talib.RSI(df['close'], timeperiod=21)
        df['rsi_overbought'] = (df['rsi_14'] > 70).astype(int)
        df['rsi_oversold'] = (df['rsi_14'] < 30).astype(int)
        df['rsi_divergence'] = df['rsi_14'] - df['rsi_14'].shift(5)

        # Moving Averages expandidas
        df['sma_10'] = talib.SMA(df['close'], timeperiod=10)
        df['sma_20'] = talib.SMA(df['close'], timeperiod=20)
        df['sma_50'] = talib.SMA(df['close'], timeperiod=50)
        df['ema_8'] = talib.EMA(df['close'], timeperiod=8)
        df['ema_12'] = talib.EMA(df['close'], timeperiod=12)
        df['ema_26'] = talib.EMA(df['close'], timeperiod=26)

        # Cruces de medias móviles
        df['sma_cross_20_50'] = np.where(df['sma_20'] > df['sma_50'], 1, -1)
        df['ema_cross_12_26'] = np.where(df['ema_12'] > df['ema_26'], 1, -1)
        df['price_above_sma20'] = np.where(df['close'] > df['sma_20'], 1, 0)

        # MACD mejorado
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        df['macd_momentum'] = df['macd_hist'] - df['macd_hist'].shift(1)
        df['macd_strength'] = abs(df['macd'] - df['macd_signal'])

        # Bollinger Bands avanzadas
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'])
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = (df['bb_width'] < df['bb_width'].rolling(20).quantile(0.2)).astype(int)

        # ATR - CRÍTICO PARA STOP LOSS Y TAKE PROFIT
        df['atr_14'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=14)
        df['atr_7'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=7)
        df['atr'] = df['atr_14']  # Mantener compatibilidad
        df['atr_ratio'] = df['atr'] / df['close']
        df['atr_trend'] = df['atr'] / df['atr'].rolling(20).mean()

        # Volume indicators avanzados
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        df['volume_trend'] = df['volume'].rolling(5).mean() / df['volume'].rolling(20).mean()
        df['obv'] = talib.OBV(df['close'], df['volume'])
        df['obv_trend'] = df['obv'] / df['obv'].rolling(20).mean()

        # Indicadores de momentum avanzados
        df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
        df['cci'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
        df['williams_r'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
        df['roc'] = talib.ROC(df['close'], timeperiod=10)
        df['mom'] = talib.MOM(df['close'], timeperiod=10)

        # Stochastic mejorado
        df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high'], df['low'], df['close'])
        df['stoch_rsi'] = talib.STOCHRSI(df['close'])
        df['stoch_cross'] = np.where(df['stoch_k'] > df['stoch_d'], 1, -1)

        # MFI y análisis de flujo de dinero
        df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['volume'], timeperiod=14)
        df['mfi_overbought'] = (df['mfi'] > 80).astype(int)
        df['mfi_oversold'] = (df['mfi'] < 20).astype(int)

        return df

    @staticmethod
    def add_pattern_features(df):
        """Patrones de velas japonesas y estructura de mercado avanzados"""
        # Patrones básicos mejorados
        df['is_green'] = (df['close'] > df['open']).astype(int)
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['close']
        df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['close']
        df['total_range'] = (df['high'] - df['low']) / df['close']

        # Patrones consecutivos expandidos
        df['green_streak'] = df['is_green'].rolling(3).sum()
        df['red_streak'] = (1 - df['is_green']).rolling(3).sum()
        df['consecutive_direction'] = df['is_green'].rolling(5).sum()

        # Patrones de velas japonesas clásicos
        df['doji'] = (df['body_size'] < 0.002).astype(int)
        df['hammer'] = ((df['lower_shadow'] > 2 * df['body_size']) &
                       (df['upper_shadow'] < df['body_size'])).astype(int)
        df['shooting_star'] = ((df['upper_shadow'] > 2 * df['body_size']) &
                              (df['lower_shadow'] < df['body_size'])).astype(int)
        df['spinning_top'] = ((df['upper_shadow'] > df['body_size']) &
                             (df['lower_shadow'] > df['body_size']) &
                             (df['body_size'] < 0.005)).astype(int)

        # Análisis de estructura de mercado
        df['higher_high'] = (df['high'] > df['high'].shift(1)).astype(int)
        df['lower_low'] = (df['low'] < df['low'].shift(1)).astype(int)
        df['inside_bar'] = ((df['high'] < df['high'].shift(1)) &
                           (df['low'] > df['low'].shift(1))).astype(int)
        df['outside_bar'] = ((df['high'] > df['high'].shift(1)) &
                            (df['low'] < df['low'].shift(1))).astype(int)

        return df

    @staticmethod
    def add_market_structure_features(df):
        """Features de estructura de mercado y contexto"""
        # Niveles de soporte y resistencia
        df['resistance_level'] = df['high'].rolling(20).max()
        df['support_level'] = df['low'].rolling(20).min()
        df['distance_to_resistance'] = (df['resistance_level'] - df['close']) / df['close']
        df['distance_to_support'] = (df['close'] - df['support_level']) / df['close']

        # Análisis de tendencia
        df['trend_strength'] = (df['close'] - df['close'].shift(20)) / df['close'].shift(20)
        df['trend_consistency'] = df['returns'].rolling(10).apply(lambda x: (x > 0).sum() / len(x))

        # Análisis de tiempo
        df['hour'] = df.index.hour if hasattr(df.index, 'hour') else 0
        df['day_of_week'] = df.index.dayofweek if hasattr(df.index, 'dayofweek') else 0
        df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)

        # Features de contexto de mercado
        df['volume_price_trend'] = df['volume'] * df['returns']
        df['price_volume_correlation'] = df['returns'].rolling(20).corr(df['volume_ratio'])

        return df

    @staticmethod
    def create_features(df):
        """Pipeline completo de features avanzados"""
        logger.log("🔧 Creando features avanzados...")

        # Aplicar todos los grupos de features
        df = FeatureEngineer.add_price_features(df)
        df = FeatureEngineer.add_technical_indicators(df)
        df = FeatureEngineer.add_pattern_features(df)
        df = FeatureEngineer.add_market_structure_features(df)

        # Limpiar NaN e infinitos
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(0)

        # Filtrar volatilidad extrema si está habilitado
        if VOLATILITY_FILTER:
            volatility_mask = df['volatility'] <= MAX_VOLATILITY_THRESHOLD
            extreme_vol_count = (~volatility_mask).sum()
            if extreme_vol_count > 0:
                logger.log(f"🔍 Filtradas {extreme_vol_count} velas por volatilidad extrema")

        logger.log(f"✅ Features creados: {len(df.columns)} columnas")
        logger.log(f"📊 Datos finales: {len(df)} filas")
        return df

    @staticmethod
    def select_best_features(df, target_col, max_features=50):
        """Selección automática de mejores features"""
        if not FEATURE_SELECTION_ENABLED:
            return df

        logger.log("🎯 Seleccionando mejores features...")

        # Excluir columnas no-feature
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp',
                       target_col, 'future_return', 'target', 'target_ml']
        feature_cols = [col for col in df.columns if col not in exclude_cols]

        if len(feature_cols) <= max_features:
            return df

        # Calcular correlación con el target
        correlations = {}
        for col in feature_cols:
            try:
                corr = abs(df[col].corr(df[target_col]))
                if not np.isnan(corr):
                    correlations[col] = corr
            except:
                pass

        # Seleccionar top features
        top_features = sorted(correlations.items(), key=lambda x: x[1], reverse=True)[:max_features]
        selected_features = [feat[0] for feat in top_features]

        # Mantener columnas esenciales
        essential_cols = ['open', 'high', 'low', 'close', 'volume', 'timestamp']
        final_cols = essential_cols + selected_features + [target_col, 'future_return', 'target', 'target_ml']
        final_cols = [col for col in final_cols if col in df.columns]

        logger.log(f"📈 Seleccionados {len(selected_features)} mejores features de {len(feature_cols)}")

        return df[final_cols]

class SignalGenerator:
    """Generador de señales ultra mejorado con ML avanzado"""

    def __init__(self):
        self.models = {}
        self.ensemble = None
        self.scaler = StandardScaler()
        self.feature_cols = None
        self.class_weights = None
        self.model_weights = {}
        self.validation_scores = {}

    def prepare_data(self, df, lookback=LOOKBACK):
        """Prepara datos para ML con umbrales optimizados y validación"""
        logger.log("📊 Preparando datos para ML...")

        # Crear target con umbrales más inteligentes basados en volatilidad
        df['future_return'] = df['close'].pct_change(3).shift(-3)

        # Umbrales dinámicos basados en volatilidad del mercado
        volatility = df['returns'].rolling(20).std().fillna(0.01)
        dynamic_threshold = volatility * 0.5  # 50% de la volatilidad como umbral

        # Aplicar umbrales dinámicos con mínimo
        min_threshold = 0.0003  # Umbral mínimo
        upper_threshold = np.maximum(dynamic_threshold, min_threshold)
        lower_threshold = -upper_threshold

        df['target'] = np.where(df['future_return'] > upper_threshold, 1,  # LONG
                               np.where(df['future_return'] < lower_threshold, -1, 0))  # SHORT

        # Para ML necesitamos 0, 1, 2
        df['target_ml'] = df['target'] + 1

        # Aplicar selección de features si está habilitada
        if FEATURE_SELECTION_ENABLED:
            df = FeatureEngineer.select_best_features(df, 'target_ml')

        # Seleccionar features
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'future_return', 'target', 'target_ml', 'timestamp']
        self.feature_cols = [col for col in df.columns if col not in exclude_cols]

        # Eliminar filas con NaN
        df_clean = df.dropna()

        if len(df_clean) < lookback + 20:
            logger.log("⚠️ No hay suficientes datos para entrenamiento", "WARNING")
            return None, None

        X = df_clean[self.feature_cols].values
        y = df_clean['target_ml'].values

        # Validar distribución de clases
        unique, counts = np.unique(y, return_counts=True)
        class_distribution = dict(zip(unique, counts))

        logger.log("📊 Distribución de clases en datos:")
        for val, count in zip(unique, counts):
            label = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(val, "UNKNOWN")
            percentage = count/len(y)*100
            logger.log(f"  • {label}: {count} ({percentage:.1f}%)")

        # Verificar que tenemos suficientes ejemplos de cada clase
        min_class_size = max(10, len(y) * 0.05)  # Mínimo 5% o 10 ejemplos
        for class_val, count in class_distribution.items():
            if count < min_class_size:
                logger.log(f"⚠️ Clase {class_val} tiene pocos ejemplos ({count})", "WARNING")

        return X, y

    def train_models(self, X_train, y_train):
        """Entrena ensemble avanzado de modelos con validación temporal"""
        logger.log("🤖 Entrenando ensemble avanzado de modelos...")

        # Calcular pesos de clases para balance
        classes = np.unique(y_train)
        weights = compute_class_weight('balanced', classes=classes, y=y_train)
        self.class_weights = dict(zip(classes, weights))

        logger.log(f"⚖️ Pesos de clases calculados: {self.class_weights}")

        # Modelos base optimizados con hiperparámetros mejorados
        models = {
            'rf': RandomForestClassifier(
                n_estimators=100,  # Incrementado
                max_depth=8,       # Más profundo
                min_samples_split=10,  # Menos restrictivo
                min_samples_leaf=5,    # Menos restrictivo
                max_features='sqrt',
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights
            ),
            'xgb': XGBClassifier(
                n_estimators=100,
                max_depth=4,       # Incrementado
                learning_rate=0.05,  # Incrementado
                reg_alpha=0.5,     # Reducido
                reg_lambda=0.5,    # Reducido
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss',
                verbosity=0
            ),
            'lgb': lgb.LGBMClassifier(
                n_estimators=100,
                max_depth=4,
                learning_rate=0.05,
                num_leaves=31,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights,
                verbosity=-1
            ),
            'gb': GradientBoostingClassifier(
                n_estimators=100,
                max_depth=4,
                learning_rate=0.05,
                subsample=0.8,
                random_state=42
            ),
            'lr': LogisticRegression(
                C=1.0,  # Incrementado
                max_iter=2000,  # Incrementado
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights
            )
        }

        # Entrenar modelos con validación
        logger.log("🔄 Entrenando modelos individuales...")
        for name, model in models.items():
            try:
                logger.log(f"  • Entrenando {name}...")
                model.fit(X_train, y_train)

                # Calcular score de entrenamiento
                train_score = model.score(X_train, y_train)
                self.validation_scores[name] = train_score

                self.models[name] = model
                logger.log(f"  ✅ {name}: Score = {train_score:.3f}")

            except Exception as e:
                logger.log(f"  ❌ Error entrenando {name}: {str(e)}", "ERROR")

        # Calcular pesos dinámicos basados en rendimiento
        if len(self.models) > 0:
            total_score = sum(self.validation_scores.values())
            for name in self.models.keys():
                self.model_weights[name] = self.validation_scores[name] / total_score

            logger.log("📊 Pesos de modelos calculados:")
            for name, weight in self.model_weights.items():
                logger.log(f"  • {name}: {weight:.3f}")

            # Crear ensemble ponderado
            estimators = [(name, model) for name, model in self.models.items()]
            self.ensemble = VotingClassifier(
                estimators=estimators,
                voting='soft'
            )
            self.ensemble.fit(X_train, y_train)

            ensemble_score = self.ensemble.score(X_train, y_train)
            logger.log(f"✅ Ensemble creado - Score: {ensemble_score:.3f}")
            logger.log(f"🎯 Modelos activos: {len(self.models)}")
        else:
            logger.log("❌ No se pudo entrenar ningún modelo", "ERROR")

    def generate_signals(self, df):
        """Genera señales avanzadas con validación de consenso y calidad"""
        if not self.models or not self.ensemble:
            logger.log("⚠️ No hay modelos entrenados", "WARNING")
            return pd.DataFrame()

        X, _ = self.prepare_data(df)
        if X is None:
            return pd.DataFrame()

        # Predicciones del ensemble y modelos individuales
        try:
            # Predicciones del ensemble principal
            ensemble_predictions = self.ensemble.predict(X)
            ensemble_probabilities = self.ensemble.predict_proba(X)

            # Predicciones de modelos individuales para consenso
            individual_predictions = {}
            individual_probabilities = {}

            for name, model in self.models.items():
                try:
                    pred = model.predict(X)
                    prob = model.predict_proba(X)
                    individual_predictions[name] = pred
                    individual_probabilities[name] = prob
                except Exception as e:
                    logger.log(f"⚠️ Error en predicción de {name}: {str(e)}", "WARNING")

            # Calcular consenso entre modelos
            if len(individual_predictions) > 1:
                # Convertir predicciones a matriz
                pred_matrix = np.array(list(individual_predictions.values()))

                # Calcular acuerdo por posición
                agreement_scores = []
                for i in range(pred_matrix.shape[1]):
                    predictions_at_i = pred_matrix[:, i]
                    # Calcular porcentaje de acuerdo con la predicción más común
                    most_common = stats.mode(predictions_at_i, keepdims=True)[0][0]
                    agreement = np.mean(predictions_at_i == most_common)
                    agreement_scores.append(agreement)

                agreement_scores = np.array(agreement_scores)
            else:
                agreement_scores = np.ones(len(ensemble_predictions))

            # Calcular confianza combinada (ensemble + consenso)
            ensemble_confidence = np.max(ensemble_probabilities, axis=1)
            combined_confidence = ensemble_confidence * agreement_scores

            # Filtrar por confianza mínima Y consenso mínimo
            confidence_mask = combined_confidence >= MIN_CONFIDENCE
            consensus_mask = agreement_scores >= ENSEMBLE_MIN_AGREEMENT
            quality_mask = confidence_mask & consensus_mask

            # Aplicar filtros
            filtered_predictions = np.where(quality_mask, ensemble_predictions, 1)  # 1 = NEUTRAL

            # Mapear predicciones: 0=SHORT, 1=NEUTRAL, 2=LONG
            signal_map = {0: -1, 1: 0, 2: 1}
            mapped_signals = np.array([signal_map.get(int(p), 0) for p in filtered_predictions])

            # Añadir ATR para cálculo de TP/SL
            atr_values = df['atr'].iloc[-len(ensemble_predictions):].values if 'atr' in df.columns else np.full(len(ensemble_predictions), df['close'].mean() * 0.005)

            # Crear DataFrame de señales
            signals = pd.DataFrame({
                'timestamp': df.index[-len(ensemble_predictions):],
                'signal': mapped_signals,
                'confidence': combined_confidence,
                'ensemble_confidence': ensemble_confidence,
                'consensus_score': agreement_scores,
                'price': df['close'].iloc[-len(ensemble_predictions):].values,
                'atr': atr_values
            })

            # Solo señales no neutrales
            signals = signals[signals['signal'] != 0]

            # VALIDACIÓN AVANZADA DE DIVERSIDAD DE SEÑALES
            if not signals.empty:
                unique_signals = signals['signal'].value_counts()
                if len(unique_signals) == 1:
                    logger.log("⚠️ Modelo generando señales uniformes - aplicando filtros adicionales", "WARNING")
                    # Intentar ajustar confianza mínima temporalmente
                    temp_min_confidence = MIN_CONFIDENCE * 0.85
                    temp_signals = signals[signals['confidence'] >= temp_min_confidence]
                    if len(temp_signals) > 0:
                        signals = temp_signals
                        logger.log(f"🔧 Ajustando confianza mínima a {temp_min_confidence:.2%}")

            # Log de debug mejorado con métricas avanzadas
            total_predictions = len(ensemble_predictions)
            unique_preds, counts = np.unique(ensemble_predictions, return_counts=True)
            logger.log(f"📊 Total predicciones: {total_predictions}")
            logger.log(f"🎯 Predicciones por tipo:")
            for pred, count in zip(unique_preds, counts):
                pred_type = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(pred, "UNKNOWN")
                logger.log(f"  • {pred_type}: {count} ({count/total_predictions*100:.1f}%)")

            # Mostrar métricas de calidad
            if len(signals) > 0:
                logger.log(f"📈 Confianza combinada: {signals['confidence'].min():.2%} - {signals['confidence'].max():.2%}")
                logger.log(f"🤝 Consenso promedio: {signals['consensus_score'].mean():.2%}")
                logger.log(f"🎯 Ensemble confianza: {signals['ensemble_confidence'].mean():.2%}")

            # Estadísticas de filtrado
            filtered_count = len(signals)
            filter_rate = (total_predictions - filtered_count) / total_predictions * 100
            logger.log(f"🔍 Filtrado: {filter_rate:.1f}% de predicciones eliminadas por baja calidad")

            logger.log(f"\n✅ Señales de alta calidad: {filtered_count}")
            if filtered_count > 0:
                logger.log(f"🔥 ¡SEÑALES ACTIVAS DETECTADAS! Sistema listo para trading.")

            return signals

        except Exception as e:
            logger.log(f"❌ Error generando señales: {str(e)}", "ERROR")
            import traceback
            logger.log(f"📋 Traceback: {traceback.format_exc()}", "ERROR")
            return pd.DataFrame()

class RiskManager:
    """Gestión de riesgo profesional"""

    def __init__(self, initial_capital=INITIAL_CAPITAL):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = []
        self.daily_pnl = 0
        self.trade_history = []

    def calculate_position_size(self, signal_confidence):
        """Calcula tamaño de posición basado en Kelly Criterion modificado"""
        # Kelly conservador
        kelly_fraction = (signal_confidence - 0.5) / 0.5
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Máximo 25% Kelly

        # Ajustar por capital actual
        position_size = self.current_capital * kelly_fraction * MAX_RISK_PER_TRADE

        # Establecer un tamaño mínimo de posición si la señal es válida
        min_position = max(50, self.current_capital * 0.005)  # $50 o 0.5% del capital
        if position_size < min_position and signal_confidence >= MIN_CONFIDENCE:
            position_size = min_position

        # Límites de seguridad
        max_position = self.current_capital * 0.1  # Máximo 10% del capital
        position_size = min(position_size, max_position)

        return position_size

    def calculate_stop_loss(self, entry_price, atr, direction):
        """Stop loss dinámico mejorado basado en ATR"""
        atr_multiplier = ATR_MULTIPLIER_SL  # Usar configuración global

        if direction == 1:  # LONG
            stop_loss = entry_price - (atr * atr_multiplier)
        else:  # SHORT
            stop_loss = entry_price + (atr * atr_multiplier)

        return stop_loss

    def calculate_take_profit(self, entry_price, atr, direction):
        """Take profit optimizado con ratio riesgo/beneficio mejorado"""
        tp_multiplier = ATR_MULTIPLIER_TP  # Usar configuración global
        tp_distance = atr * tp_multiplier

        if direction == 1:  # LONG
            take_profit = entry_price + tp_distance
        else:  # SHORT
            take_profit = entry_price - tp_distance

        return take_profit

    def calculate_trailing_stop(self, entry_price, current_price, direction, atr, best_price=None):
        """Trailing stop loss dinámico"""
        if not TRAILING_STOP_ENABLED:
            return self.calculate_stop_loss(entry_price, atr, direction)

        if best_price is None:
            best_price = current_price

        trailing_distance = atr * (ATR_MULTIPLIER_SL * 0.8)  # Más ajustado que SL inicial

        if direction == 1:  # LONG
            # Solo mover el stop loss hacia arriba
            trailing_stop = best_price - trailing_distance
            initial_stop = self.calculate_stop_loss(entry_price, atr, direction)
            return max(trailing_stop, initial_stop)
        else:  # SHORT
            # Solo mover el stop loss hacia abajo
            trailing_stop = best_price + trailing_distance
            initial_stop = self.calculate_stop_loss(entry_price, atr, direction)
            return min(trailing_stop, initial_stop)

    def is_stop_loss_hit(self, current_price, stop_loss, direction):
        """Detección robusta de stop loss con tolerancia"""
        tolerance = TP_SL_TOLERANCE

        if direction == 1:  # LONG
            # Para LONG, SL se activa si precio <= stop_loss (con tolerancia)
            return current_price <= (stop_loss * (1 + tolerance))
        else:  # SHORT
            # Para SHORT, SL se activa si precio >= stop_loss (con tolerancia)
            return current_price >= (stop_loss * (1 - tolerance))

    def is_take_profit_hit(self, current_price, take_profit, direction):
        """Detección robusta de take profit con tolerancia"""
        tolerance = TP_SL_TOLERANCE

        if direction == 1:  # LONG
            # Para LONG, TP se activa si precio >= take_profit (con tolerancia)
            return current_price >= (take_profit * (1 - tolerance))
        else:  # SHORT
            # Para SHORT, TP se activa si precio <= take_profit (con tolerancia)
            return current_price <= (take_profit * (1 + tolerance))

    def check_daily_loss_limit(self):
        """Verifica límite de pérdida diaria"""
        daily_loss_pct = abs(self.daily_pnl / self.initial_capital)

        if daily_loss_pct >= MAX_DAILY_LOSS:
            logger.log(f"🛑 Límite diario alcanzado: {daily_loss_pct:.2%}", "WARNING")
            return True
        return False

    def update_capital(self, pnl):
        """Actualiza capital y estadísticas"""
        self.current_capital += pnl
        self.daily_pnl += pnl

        # Log estado
        logger.log(f"💰 Capital: ${self.current_capital:.2f} | PnL Diario: ${self.daily_pnl:.2f}")

class SimulatedTrader:
    """Trading en modo simulación"""

    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()
        self.active_positions = []

    def train_system(self, df):
        """Entrena el sistema con datos históricos"""
        # Crear features
        df_features = self.feature_engineer.create_features(df.copy())

        # Preparar datos
        X, y = self.signal_generator.prepare_data(df_features)

        if X is not None and len(X) > 100:
            # Split 80/20
            split_idx = int(0.8 * len(X))
            X_train, y_train = X[:split_idx], y[:split_idx]

            # Escalar datos
            X_train_scaled = self.signal_generator.scaler.fit_transform(X_train)

            # Entrenar modelos
            self.signal_generator.train_models(X_train_scaled, y_train)

            logger.log("✅ Sistema entrenado y listo")
            logger.log(f"📊 Modelos activos: {list(self.signal_generator.models.keys())}")
            return True

        return False

    def execute_trade(self, signal, current_df, current_price=None):
        """Ejecuta trade en simulación con información completa"""
        # Calcular parámetros
        position_size = self.risk_manager.calculate_position_size(signal['confidence'])

        # Obtener ATR actual del DataFrame
        if 'atr' in current_df.columns and not current_df['atr'].empty:
            current_atr = current_df['atr'].iloc[-1]
            if pd.isna(current_atr) or current_atr <= 0:
                current_atr = current_df['close'].iloc[-1] * 0.005
        else:
            current_atr = current_df['close'].iloc[-1] * 0.005

        # Usar el precio actual real para la entrada
        entry_price = current_price if current_price is not None else current_df['close'].iloc[-1]

        stop_loss = self.risk_manager.calculate_stop_loss(
            entry_price, current_atr, signal['signal']
        )

        take_profit = self.risk_manager.calculate_take_profit(
            entry_price, current_atr, signal['signal']
        )

        trade = {
            'timestamp': signal['timestamp'],
            'type': 'LONG' if signal['signal'] == 1 else 'SHORT',
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': position_size,
            'confidence': signal['confidence'],
            'status': 'OPEN',
            'entry_time': datetime.datetime.now()
        }

        self.active_positions.append(trade)

        # Log detallado
        logger.log(f"\n{'='*60}")
        logger.log(f"🎯 NUEVA POSICIÓN ABIERTA:")
        logger.log(f"├─ Tipo: {'🟢 LONG' if trade['type'] == 'LONG' else '🔴 SHORT'}")
        logger.log(f"├─ Entrada: ${trade['entry_price']:.2f}")
        logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Tamaño: ${position_size:.2f}")
        logger.log(f"├─ Confianza: {trade['confidence']:.2%}")
        logger.log(f"└─ Risk/Reward: 1:{abs(take_profit-trade['entry_price'])/abs(stop_loss-trade['entry_price']):.1f}")
        logger.log(f"{'='*60}\n")

        return trade

    def update_positions(self, current_price, current_atr=None):
        """Actualiza posiciones abiertas con detección robusta de TP/SL y trailing stop"""
        for position in self.active_positions:
            if position['status'] != 'OPEN':
                continue

            direction = 1 if position['type'] == 'LONG' else -1

            # Actualizar mejor precio si es necesario
            if 'best_price' not in position:
                position['best_price'] = position['entry_price']

            # Actualizar mejor precio
            if position['type'] == 'LONG' and current_price > position['best_price']:
                position['best_price'] = current_price
            elif position['type'] == 'SHORT' and current_price < position['best_price']:
                position['best_price'] = current_price

            # Actualizar trailing stop si está habilitado
            if TRAILING_STOP_ENABLED and current_atr is not None:
                new_stop = self.risk_manager.calculate_trailing_stop(
                    position['entry_price'],
                    current_price,
                    direction,
                    current_atr,
                    position['best_price']
                )

                # Solo actualizar si el nuevo stop es mejor
                if position['type'] == 'LONG' and new_stop > position['stop_loss']:
                    old_stop = position['stop_loss']
                    position['stop_loss'] = new_stop
                    logger.log(f"📈 Trailing stop actualizado LONG: ${old_stop:.2f} → ${new_stop:.2f}")
                elif position['type'] == 'SHORT' and new_stop < position['stop_loss']:
                    old_stop = position['stop_loss']
                    position['stop_loss'] = new_stop
                    logger.log(f"📉 Trailing stop actualizado SHORT: ${old_stop:.2f} → ${new_stop:.2f}")

            # Calcular distancias para alertas
            if position['type'] == 'LONG':
                dist_to_sl = (current_price - position['stop_loss']) / position['stop_loss'] * 100
                dist_to_tp = (position['take_profit'] - current_price) / current_price * 100

                # Alertas de proximidad mejoradas
                if 0 < dist_to_sl < 1.0:  # Dentro del 1%
                    logger.log(f"🚨 ALERTA CRÍTICA: LONG muy cerca de SL! Distancia: {dist_to_sl:.3f}%")
                elif 0 < dist_to_tp < 1.0:  # Dentro del 1%
                    logger.log(f"🎯 ALERTA: LONG cerca de TP! Distancia: {dist_to_tp:.3f}%")

            else:  # SHORT
                dist_to_sl = (position['stop_loss'] - current_price) / current_price * 100
                dist_to_tp = (current_price - position['take_profit']) / position['take_profit'] * 100

                # Alertas de proximidad mejoradas
                if 0 < dist_to_sl < 1.0:  # Dentro del 1%
                    logger.log(f"🚨 ALERTA CRÍTICA: SHORT muy cerca de SL! Distancia: {dist_to_sl:.3f}%")
                elif 0 < dist_to_tp < 1.0:  # Dentro del 1%
                    logger.log(f"🎯 ALERTA: SHORT cerca de TP! Distancia: {dist_to_tp:.3f}%")

            # Usar detección robusta de TP/SL
            if self.risk_manager.is_stop_loss_hit(current_price, position['stop_loss'], direction):
                logger.log(f"🛑 STOP LOSS ACTIVADO: {position['type']} @ ${current_price:.2f}")
                self.close_position(position, current_price, 'STOP_LOSS')
            elif self.risk_manager.is_take_profit_hit(current_price, position['take_profit'], direction):
                logger.log(f"🎯 TAKE PROFIT ACTIVADO: {position['type']} @ ${current_price:.2f}")
                self.close_position(position, current_price, 'TAKE_PROFIT')

    def close_position(self, position, exit_price, reason):
        """Cierra posición y calcula PnL con detalles"""
        if position['type'] == 'LONG':
            pnl = (exit_price - position['entry_price']) / position['entry_price'] * position['position_size']
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
        else:  # SHORT
            pnl = (position['entry_price'] - exit_price) / position['entry_price'] * position['position_size']
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100

        position['exit_price'] = exit_price
        position['pnl'] = pnl
        position['pnl_pct'] = pnl_pct
        position['status'] = 'CLOSED'
        position['exit_reason'] = reason
        position['exit_time'] = datetime.datetime.now()
        position['duration'] = (position['exit_time'] - position['entry_time']).seconds // 60

        # Actualizar capital
        self.risk_manager.update_capital(pnl)

        # Log detallado del resultado
        emoji = "💰" if pnl > 0 else "📉"
        logger.log(f"\n{emoji} POSICIÓN CERRADA:")
        logger.log(f"├─ Tipo: {position['type']}")
        logger.log(f"├─ Entrada: ${position['entry_price']:.2f}")
        logger.log(f"├─ Salida: ${exit_price:.2f}")
        logger.log(f"├─ Razón: {reason}")
        logger.log(f"├─ PnL: ${pnl:.2f} ({pnl_pct:.2f}%)")
        logger.log(f"├─ Duración: {position['duration']} minutos")
        logger.log(f"└─ Capital actualizado: ${self.risk_manager.current_capital:.2f}")

        # Si es el primer trade cerrado, mostrar información adicional
        closed_count = len([p for p in self.active_positions if p['status'] == 'CLOSED'])
        if closed_count == 1:
            logger.log(f"\n🎉 ¡PRIMER TRADE COMPLETADO!")
            logger.log(f"💡 El sistema está funcionando correctamente.")
            logger.log(f"📊 Continúa monitoreando para ver más trades...")

    def generate_report(self):
        """Genera reporte de rendimiento"""
        closed_positions = [p for p in self.active_positions if p['status'] == 'CLOSED']

        # Calcular métricas básicas
        current_capital = self.risk_manager.current_capital
        initial_capital = self.risk_manager.initial_capital
        roi = (current_capital - initial_capital) / initial_capital * 100

        if not closed_positions:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_pnl': 0,
                'sharpe_ratio': 0,
                'current_capital': current_capital,
                'roi': roi
            }

        # Calcular métricas
        wins = [p for p in closed_positions if p['pnl'] > 0]
        losses = [p for p in closed_positions if p['pnl'] < 0]

        win_rate = len(wins) / len(closed_positions) * 100

        total_wins = sum(p['pnl'] for p in wins) if wins else 0
        total_losses = abs(sum(p['pnl'] for p in losses)) if losses else 1

        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')

        # Calcular Sharpe Ratio
        returns = [p['pnl'] / p['position_size'] for p in closed_positions]
        if len(returns) > 1:
            sharpe = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        else:
            sharpe = 0

        report = {
            'total_trades': len(closed_positions),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_pnl': sum(p['pnl'] for p in closed_positions),
            'sharpe_ratio': sharpe,
            'current_capital': current_capital,
            'roi': roi
        }

        return report

class LiveTradingBot:
    """Bot principal para trading en vivo (simulado)"""

    def __init__(self):
        self.trader = SimulatedTrader()
        self.is_trained = False
        self.last_signal_time = None
        self.timeframes = ['15m', '1h']
        self.total_trades_executed = 0

    def show_market_summary(self, df):
        """Muestra resumen rápido del mercado"""
        current_price = df['close'].iloc[-1]
        price_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
        rsi = talib.RSI(df['close'])[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volume_ratio = df['volume'].iloc[-1] / volume_avg

        # Obtener símbolo del activo
        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n💹 MERCADO {asset_symbol}: ${current_price:.2f} ({price_change:+.2f}%) | "
                  f"RSI: {rsi:.0f} {'🔴' if rsi > 70 else '🟢' if rsi < 30 else '⚪'} | "
                  f"Vol: {'🔥' if volume_ratio > 1.5 else '📊'} x{volume_ratio:.1f}")

    def initialize(self):
        """Inicializa el bot con datos históricos"""
        logger.log("🚀 Inicializando Trading Bot Profesional v3.0 ULTRA MEJORADO")
        logger.log(f"💱 Activo seleccionado: {SYMBOL}")
        logger.log("📚 Descargando datos históricos para entrenamiento avanzado...")

        # Obtener más datos históricos para mejor entrenamiento
        df = self.trader.data_manager.get_live_data(timeframe='15m', limit=1500)

        if not df.empty:
            logger.log("🤖 Entrenando ensemble avanzado de 5 modelos ML...")
            logger.log("🔧 Aplicando mejoras v3.0: consenso, features expandidos, TP/SL robusto")
            self.is_trained = self.trader.train_system(df)

            if self.is_trained:
                logger.log("✅ Bot v3.0 inicializado correctamente")
                logger.log(f"📊 Sistema listo con {len(self.trader.signal_generator.models)} modelos avanzados")
                logger.log("✨ Nuevas características activas:")
                logger.log("  • Consenso inteligente entre modelos")
                logger.log("  • Trailing stop loss automático")
                logger.log("  • Detección TP/SL con tolerancias")
                logger.log("  • Features técnicos expandidos (50+)")
                logger.log("  • Umbrales dinámicos de volatilidad")
                self.show_next_predictions(df)
            else:
                logger.log("❌ Error en inicialización", "ERROR")
        else:
            logger.log("❌ No se pudieron obtener datos", "ERROR")

    def show_next_predictions(self, df):
        """Muestra próximas predicciones con niveles de TP/SL"""
        logger.log("\n" + "="*60)
        logger.log("🔮 PRÓXIMAS PREDICCIONES")
        logger.log("="*60)

        # Crear features
        df_features = self.trader.feature_engineer.create_features(df.copy())

        # Generar señales
        signals = self.trader.signal_generator.generate_signals(df_features)

        if not signals.empty:
            # Mostrar últimas 5 señales con TP/SL
            logger.log("\n📊 SEÑALES DE TRADING:")
            for _, signal in signals.tail(5).iterrows():
                direction = "🟢 LONG" if signal['signal'] == 1 else "🔴 SHORT"

                # Calcular TP/SL
                atr = signal['atr'] if signal['atr'] > 0 else signal['price'] * 0.005
                if signal['signal'] == 1:  # LONG
                    stop_loss = signal['price'] - (atr * 1.5)
                    take_profit = signal['price'] + (atr * 2.25)
                else:  # SHORT
                    stop_loss = signal['price'] + (atr * 1.5)
                    take_profit = signal['price'] - (atr * 2.25)

                logger.log(f"\n{direction} @ ${signal['price']:.2f}")
                logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-signal['price'])/signal['price']*100:.2f}%)")
                logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-signal['price'])/signal['price']*100:.2f}%)")
                logger.log(f"├─ Confianza: {signal['confidence']:.2%}")
                logger.log(f"└─ Hora: {signal['timestamp']}")
        else:
            logger.log("❌ No hay señales de alta confianza en este momento")

        # Mostrar estado del mercado detallado
        current_price = df['close'].iloc[-1]
        rsi = talib.RSI(df['close'])[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volatility = df['close'].pct_change().rolling(20).std().iloc[-1] * 100

        # Nuevos indicadores
        adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
        cci = talib.CCI(df['high'], df['low'], df['close'])[-1]
        mfi = talib.MFI(df['high'], df['low'], df['close'], df['volume'])[-1]

        trend = "ALCISTA 📈" if df['close'].iloc[-1] > df['close'].iloc[-20] else "BAJISTA 📉"
        momentum = "FUERTE" if abs(df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5] > 0.01 else "DÉBIL"

        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n📊 ESTADO DEL MERCADO {asset_symbol}:")
        logger.log(f"├─ Precio actual: ${current_price:.2f}")
        logger.log(f"├─ RSI: {rsi:.2f} {'(Sobrecompra)' if rsi > 70 else '(Sobreventa)' if rsi < 30 else '(Neutral)'}")
        logger.log(f"├─ ADX: {adx:.2f} {'(Tendencia fuerte)' if adx > 25 else '(Tendencia débil)'}")
        logger.log(f"├─ CCI: {cci:.2f}")
        logger.log(f"├─ MFI: {mfi:.2f}")
        logger.log(f"├─ SMA 20: ${sma_20:.2f} {'↑' if current_price > sma_20 else '↓'}")
        logger.log(f"├─ SMA 50: ${sma_50:.2f} {'↑' if current_price > sma_50 else '↓'}")
        logger.log(f"├─ Tendencia: {trend} ({momentum})")
        logger.log(f"├─ Volatilidad: {volatility:.2f}% {'🔥 ALTA' if volatility > 2 else '✅ NORMAL'}")
        logger.log(f"└─ Volumen: {'📊 Alto' if df['volume'].iloc[-1] > volume_avg * 1.5 else '📉 Normal'}")

    def run_live_simulation(self):
        """Ejecuta bot en modo simulación continua"""
        if not self.is_trained:
            logger.log("❌ Bot no está entrenado", "ERROR")
            logger.log("💡 Por favor, seleccione opción 1 para inicializar el sistema primero")
            return

        asset_symbol = SYMBOL.split('/')[0]

        logger.log(f"\n🤖 INICIANDO TRADING v3.0 EN MODO SIMULACIÓN - {asset_symbol}")
        logger.log(f"💰 Capital inicial: ${self.trader.risk_manager.initial_capital}")
        logger.log(f"📍 Posiciones máximas: {MAX_POSITIONS}")
        logger.log("\n📌 CARACTERÍSTICAS v3.0 ACTIVAS:")
        logger.log("📌 • Ensemble de 5 modelos ML con consenso inteligente")
        logger.log("📌 • Detección TP/SL robusta con tolerancias")
        logger.log("📌 • Trailing stop loss automático")
        logger.log("📌 • 50+ features técnicos avanzados")
        logger.log("📌 • Umbrales dinámicos basados en volatilidad")
        logger.log(f"📌 • Confianza mínima: {MIN_CONFIDENCE:.0%} | Consenso mínimo: {ENSEMBLE_MIN_AGREEMENT:.0%}")
        logger.log(f"📌 • Tolerancia TP/SL: {TP_SL_TOLERANCE:.2%} | Trailing stop: {'✅' if TRAILING_STOP_ENABLED else '❌'}\n")

        cycle = 0
        while True:
            try:
                cycle += 1
                logger.log(f"\n{'='*60}")
                logger.log(f"📍 Ciclo #{cycle} - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # Verificar límite diario
                if self.trader.risk_manager.check_daily_loss_limit():
                    logger.log("🛑 Límite diario alcanzado - Trading pausado")
                    time.sleep(3600)
                    continue

                # Obtener datos actuales
                df = self.trader.data_manager.get_live_data(timeframe='15m', limit=200)

                if df.empty:
                    logger.log("⚠️ No hay datos disponibles", "WARNING")
                    time.sleep(60)
                    continue

                # Precio actual
                current_price = df['close'].iloc[-1]

                # Mostrar resumen de mercado
                if cycle <= 10 or cycle % 3 == 0:
                    self.show_market_summary(df)

                # Mostrar información del ciclo
                if cycle == 1:
                    logger.log(f"├─ Sistema: ✅ ACTIVO")
                    logger.log(f"├─ Modelos ML: {len(self.trader.signal_generator.models)} activos")
                    logger.log(f"├─ Confianza mínima: {MIN_CONFIDENCE:.0%}")
                logger.log(f"├─ Precio {asset_symbol}: ${current_price:.2f}")
                logger.log(f"├─ Posiciones abiertas: {len([p for p in self.trader.active_positions if p['status'] == 'OPEN'])}/{MAX_POSITIONS}")
                logger.log(f"├─ Trades ejecutados: {self.total_trades_executed}")
                logger.log(f"└─ Capital: ${self.trader.risk_manager.current_capital:.2f}")

                # Obtener ATR actual para trailing stop
                current_atr = None
                if 'atr' in df.columns and not df['atr'].empty:
                    current_atr = df['atr'].iloc[-1]
                    if pd.isna(current_atr) or current_atr <= 0:
                        current_atr = df['close'].iloc[-1] * 0.005
                else:
                    current_atr = df['close'].iloc[-1] * 0.005

                # Actualizar posiciones con precio y ATR actual
                self.trader.update_positions(current_price, current_atr)

                # Generar nuevas señales desde ciclo 2
                if cycle >= 2:
                    logger.log(f"\n🔍 [Ciclo {cycle}] Analizando mercado para oportunidades...")
                    df_features = self.trader.feature_engineer.create_features(df.copy())

                    # Mostrar algunos indicadores clave
                    try:
                        current_rsi = talib.RSI(df['close'])[-1]
                        current_macd = talib.MACD(df['close'])[0][-1]
                        current_adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
                        logger.log(f"📊 Indicadores: RSI={current_rsi:.1f}, MACD={current_macd:.1f}, ADX={current_adx:.1f}")
                    except:
                        pass

                    signals = self.trader.signal_generator.generate_signals(df_features)

                    if not signals.empty:
                        logger.log(f"\n🎯 ¡{len(signals)} SEÑALES DETECTADAS!")

                        # Contar tipos de señales
                        long_signals = len(signals[signals['signal'] == 1])
                        short_signals = len(signals[signals['signal'] == -1])

                        if long_signals > 0:
                            logger.log(f"🟢 LONG: {long_signals} señales")
                        if short_signals > 0:
                            logger.log(f"🔴 SHORT: {short_signals} señales")

                        # Mostrar mejor señal
                        best_signal = signals.loc[signals['confidence'].idxmax()]
                        logger.log(f"🏆 Mejor señal: {'LONG' if best_signal['signal'] == 1 else 'SHORT'} con {best_signal['confidence']:.1%} confianza")

                        # Verificar posiciones abiertas
                        open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                        available_slots = MAX_POSITIONS - len(open_positions)

                        # Mostrar posiciones abiertas actual
                        logger.log(f"📊 Posiciones abiertas actuales: {len(open_positions)}")

                        if available_slots > 0:
                            logger.log(f"💼 Espacios disponibles para trades: {available_slots}")

                            # Tomar las mejores señales por confianza
                            signals_sorted = signals.sort_values('confidence', ascending=False)
                            signals_to_process = signals_sorted.head(available_slots)

                            for idx, (_, signal) in enumerate(signals_to_process.iterrows()):
                                logger.log(f"\n🔄 Procesando señal {idx+1}/{len(signals_to_process)}:")
                                logger.log(f"   Tipo: {'🟢 LONG' if signal['signal'] == 1 else '🔴 SHORT'}")
                                logger.log(f"   Precio: ${signal['price']:.2f}")
                                logger.log(f"   Confianza: {signal['confidence']:.2%}")

                                # Ejecutar directamente
                                logger.log("   ✅ EJECUTANDO TRADE")
                                try:
                                    self.trader.execute_trade(signal, df, current_price)
                                    self.total_trades_executed += 1
                                    logger.log("   ✅ Trade ejecutado exitosamente")
                                except Exception as e:
                                    logger.log(f"   ❌ Error ejecutando trade: {str(e)}", "ERROR")

                                # Pequeña pausa para ver la acción
                                time.sleep(1)
                        else:
                            logger.log(f"⚠️ Máximo de posiciones alcanzado ({MAX_POSITIONS}) - esperando cierre de posiciones")
                    else:
                        logger.log(f"❌ No hay señales con confianza suficiente (>{MIN_CONFIDENCE:.0%})")
                        # Mostrar info de debug
                        X, _ = self.trader.signal_generator.prepare_data(df_features)
                        if X is not None and len(X) > 0:
                            logger.log(f"🔍 Sistema analizando {len(X)} patrones de mercado...")

                # Mostrar estado de posiciones abiertas
                open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                if open_positions and cycle % 3 == 0:
                    logger.log("\n📋 POSICIONES ABIERTAS:")
                    for i, pos in enumerate(open_positions):
                        pnl_actual = ((current_price - pos['entry_price']) / pos['entry_price'] * 100
                                     if pos['type'] == 'LONG'
                                     else (pos['entry_price'] - current_price) / pos['entry_price'] * 100)

                        # Emoji según PnL
                        pnl_emoji = "🟢" if pnl_actual > 0 else "🔴" if pnl_actual < 0 else "⚪"

                        logger.log(f"{i+1}. {pos['type']} @ ${pos['entry_price']:.2f} | "
                                  f"PnL: {pnl_emoji} {pnl_actual:+.2f}% | "
                                  f"Tiempo: {(datetime.datetime.now() - pos['entry_time']).seconds // 60} min")

                # Mostrar estado cada 5 ciclos
                if cycle % 5 == 0:
                    report = self.trader.generate_report()
                    logger.log(f"\n📊 REPORTE DE RENDIMIENTO:")
                    logger.log(f"├─ Trades totales: {report['total_trades']}")
                    logger.log(f"├─ Win Rate: {report['win_rate']:.1f}%")
                    logger.log(f"├─ Profit Factor: {report['profit_factor']:.2f}")
                    logger.log(f"├─ PnL Total: ${report['total_pnl']:.2f}")
                    logger.log(f"├─ ROI: {report.get('roi', 0):.2f}%")
                    logger.log(f"└─ Capital actual: ${report['current_capital']:.2f}")

                    # Solo mostrar predicciones cada 10 ciclos
                    if cycle % 10 == 0:
                        self.show_next_predictions(df)

                # Esperar antes del próximo ciclo
                time.sleep(30)  # 30 segundos

            except KeyboardInterrupt:
                logger.log("\n🛑 Trading detenido por el usuario")
                break
            except Exception as e:
                logger.log(f"❌ Error en ciclo: {str(e)}", "ERROR")
                time.sleep(60)

        # Reporte final
        logger.log("\n" + "="*60)
        logger.log("📊 REPORTE FINAL")
        logger.log("="*60)

        final_report = self.trader.generate_report()
        for key, value in final_report.items():
            if isinstance(value, float):
                logger.log(f"{key}: {value:.2f}")
            else:
                logger.log(f"{key}: {value}")

def main():
    """Función principal"""
    asset_symbol = SYMBOL.split('/')[0]

    print("\n" + "="*60)
    print(f"🚀 {asset_symbol} TRADING BOT PROFESIONAL v3.0 ULTRA MEJORADO")
    print("\n⚡ IMPORTANTE:")
    print("1. Primero DEBES inicializar el sistema (opción 1)")
    print("2. El bot usa ML avanzado con 5 modelos y consenso inteligente")
    print("3. Sistema configurado para 1 posición máxima")
    print("4. Los resultados son 100% simulados - NO es dinero real")
    print("="*60)
    print("✅ NUEVAS MEJORAS v3.0:")
    print("  • 5 modelos ML avanzados (RF, XGB, LightGBM, GB, LR)")
    print("  • Consenso inteligente entre modelos")
    print("  • 50+ features técnicos expandidos")
    print("  • Detección TP/SL robusta con tolerancias")
    print("  • Trailing stop loss automático")
    print("  • Umbrales dinámicos basados en volatilidad")
    print("  • Selección automática de features")
    print("  • Validación temporal de modelos")
    print("  • Filtros de calidad de señales")
    print("="*60)

    bot = LiveTradingBot()

    # Menú principal
    while True:
        print("\nSeleccione opción:")
        print("1. Inicializar/Reinicializar sistema")
        print("2. Ejecutar simulación en vivo ⭐ (RECOMENDADO)")
        print("3. Ver predicciones actuales")
        print("4. Salir")

        opcion = input("\nOpción (1-4): ")

        if opcion == '1':
            print("\n🔄 Inicializando sistema...")
            print("⏳ Esto puede tomar 30-60 segundos...")
            bot.initialize()
            print("\n✅ Sistema listo para trading!")
            print("💡 Ahora seleccione opción 2 para comenzar la simulación")
        elif opcion == '2':
            if bot.is_trained:
                bot.run_live_simulation()
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de ejecutar la simulación")
        elif opcion == '3':
            if bot.is_trained:
                df = bot.trader.data_manager.get_live_data()
                bot.show_next_predictions(df)
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
        elif opcion == '4':
            print(f"👋 Gracias por usar {asset_symbol} Trading Bot Pro v2.0")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()