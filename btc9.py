# BTC Trading Bot PROFESIONAL v2.0 - VERSIÓN MEJORADA
# Sistema completo con mejoras en ML y diversificación de señales
# Diseñado para ejecutar trades reales en modo simulación

import ccxt
import pandas as pd
import numpy as np
import talib
import time
import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.utils.class_weight import compute_class_weight
from xgboost import XGBClassifier
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import warnings
warnings.filterwarnings('ignore')

# Configuración DEMO ACTIVA
LOOKBACK = 20
SYMBOL = 'BTC/USDT'  # Cambiar aquí para otro activo: 'ETH/USDT', 'SOL/USDT', etc.
INITIAL_CAPITAL = 10000
MAX_RISK_PER_TRADE = 0.02  # 2% por trade
MAX_DAILY_LOSS = 0.05  # 5% pérdida máxima diaria
MIN_WIN_RATE = 0.40  # 40% mínimo
MIN_PROFIT_FACTOR = 1.0
MIN_CONFIDENCE = 0.45  # 45% mínimo de confianza
MAX_POSITIONS = 1  # Cambiado de 3 a 1 posición máxima

# Crear directorios
for dir_name in ['modelos', 'logs', 'trades', 'reports']:
    os.makedirs(dir_name, exist_ok=True)

class TradingLogger:
    """Sistema de logging mejorado"""
    def __init__(self):
        self.log_file = f"logs/trading_{datetime.datetime.now().strftime('%Y%m%d')}.log"
        
    def log(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"{timestamp} [{level}] {message}"
        print(formatted_message)
        
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(formatted_message + "\n")
        except:
            pass

logger = TradingLogger()

class DataManager:
    """Gestión profesional de datos"""
    def __init__(self):
        self.exchange = None
        
    def get_live_data(self, symbol=SYMBOL, timeframe='15m', limit=500):
        """Obtiene datos en vivo de Binance"""
        try:
            if not self.exchange:
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'options': {'defaultType': 'spot'}
                })
            
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            logger.log(f"✅ Datos obtenidos: {len(df)} velas de {timeframe}")
            return df
            
        except Exception as e:
            logger.log(f"❌ Error obteniendo datos: {str(e)}", "ERROR")
            return self.get_mock_data(timeframe, limit)
    
    def get_mock_data(self, timeframe='15m', limit=500):
        """Datos simulados realistas para testing"""
        np.random.seed(42)
        freq_map = {'5m': '5T', '15m': '15T', '1h': '1H', '2h': '2H', '4h': '4H'}
        
        timestamps = pd.date_range(end=pd.Timestamp.now(), periods=limit, freq=freq_map[timeframe])
        
        # Simulación más realista con tendencias y volatilidad variable
        # Ajustar base_price según el activo
        base_prices = {
            'BTC/USDT': 98000,
            'ETH/USDT': 3500,
            'SOL/USDT': 140,
            'BNB/USDT': 600
        }
        base_price = base_prices.get(SYMBOL, 98000)
        
        drift = 0.0001
        volatility = 0.001
        
        prices = [base_price]
        for i in range(1, limit):
            change = drift + np.random.normal(0, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        close = np.array(prices)
        open_prices = np.roll(close, 1)
        open_prices[0] = base_price
        
        high = np.maximum(close, open_prices) * (1 + np.random.uniform(0, 0.002, limit))
        low = np.minimum(close, open_prices) * (1 - np.random.uniform(0, 0.002, limit))
        volume = np.random.lognormal(10, 0.5, limit) * 1000
        
        df = pd.DataFrame({
            'open': open_prices,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=timestamps)
        
        logger.log(f"✅ Datos simulados generados: {len(df)} velas")
        return df

class FeatureEngineer:
    """Feature Engineering robusto y probado"""
    
    @staticmethod
    def add_price_features(df):
        """Features básicos de precio"""
        # Returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Volatility
        df['volatility'] = df['returns'].rolling(20).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(50).mean()
        
        # Price ratios
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['co_ratio'] = (df['close'] - df['open']) / df['open']
        
        return df
    
    @staticmethod
    def add_technical_indicators(df):
        """Indicadores técnicos probados y mejorados"""
        # RSI
        df['rsi'] = talib.RSI(df['close'], timeperiod=14)
        df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
        df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
        
        # Moving Averages
        df['sma_20'] = talib.SMA(df['close'], timeperiod=20)
        df['sma_50'] = talib.SMA(df['close'], timeperiod=50)
        df['ema_12'] = talib.EMA(df['close'], timeperiod=12)
        df['ema_26'] = talib.EMA(df['close'], timeperiod=26)
        
        # MACD
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        
        # Bollinger Bands
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'])
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # ATR - CRÍTICO PARA STOP LOSS Y TAKE PROFIT
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'])
        df['atr_ratio'] = df['atr'] / df['close']
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # NUEVOS INDICADORES PARA DIVERSIFICAR SEÑALES
        # ADX - Average Directional Index
        df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=14)
        
        # CCI - Commodity Channel Index
        df['cci'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
        
        # Williams %R
        df['williams_r'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
        
        # Stochastic
        df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high'], df['low'], df['close'])
        
        # MFI - Money Flow Index
        df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['volume'], timeperiod=14)
        
        return df
    
    @staticmethod
    def add_pattern_features(df):
        """Patrones de velas japonesas mejorados"""
        # Patrones simples
        df['is_green'] = (df['close'] > df['open']).astype(int)
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        
        # Patrones consecutivos
        df['green_streak'] = df['is_green'].rolling(3).sum()
        df['red_streak'] = (1 - df['is_green']).rolling(3).sum()
        
        # Patrones de velas más complejos
        df['doji'] = (df['body_size'] < 0.001).astype(int)
        df['hammer'] = ((df['close'] > df['open']) & 
                       ((df['low'] - df['open']) > 2 * df['body_size']) & 
                       ((df['high'] - df['close']) < df['body_size'])).astype(int)
        
        return df
    
    @staticmethod
    def create_features(df):
        """Pipeline completo de features"""
        df = FeatureEngineer.add_price_features(df)
        df = FeatureEngineer.add_technical_indicators(df)
        df = FeatureEngineer.add_pattern_features(df)
        
        # Limpiar NaN e infinitos
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(0)
        
        logger.log(f"✅ Features creados: {len(df.columns)} columnas")
        return df

class SignalGenerator:
    """Generador de señales mejorado con balance de clases"""
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.feature_cols = None
        self.class_weights = None
        
    def prepare_data(self, df, lookback=LOOKBACK):
        """Prepara datos para ML con umbrales más sensibles"""
        # Crear target con umbrales más sensibles
        df['future_return'] = df['close'].pct_change(3).shift(-3)
        
        # CAMBIO IMPORTANTE: Umbrales más sensibles
        df['target'] = np.where(df['future_return'] > 0.0002, 1,  # LONG - más sensible
                               np.where(df['future_return'] < -0.0002, -1, 0))  # SHORT - más sensible
        
        # Para ML necesitamos 0, 1, 2
        df['target_ml'] = df['target'] + 1
        
        # Seleccionar features
        exclude_cols = ['open', 'high', 'low', 'close', 'volume', 'future_return', 'target', 'target_ml']
        self.feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        # Eliminar filas con NaN
        df_clean = df.dropna()
        
        if len(df_clean) < lookback + 10:
            logger.log("⚠️ No hay suficientes datos", "WARNING")
            return None, None
        
        X = df_clean[self.feature_cols].values
        y = df_clean['target_ml'].values
        
        # Log distribución de clases
        unique, counts = np.unique(y, return_counts=True)
        logger.log("📊 Distribución de clases en datos:")
        for val, count in zip(unique, counts):
            label = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(val, "UNKNOWN")
            logger.log(f"  • {label}: {count} ({count/len(y)*100:.1f}%)")
        
        return X, y
    
    def train_models(self, X_train, y_train):
        """Entrena ensemble de modelos con balance de clases"""
        logger.log("🤖 Entrenando modelos con balance de clases...")
        
        # Calcular pesos de clases para balance
        classes = np.unique(y_train)
        weights = compute_class_weight('balanced', classes=classes, y=y_train)
        self.class_weights = dict(zip(classes, weights))
        
        logger.log(f"⚖️ Pesos de clases calculados: {self.class_weights}")
        
        # Modelos base con balance de clases
        models = {
            'rf': RandomForestClassifier(
                n_estimators=50,
                max_depth=5,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights
            ),
            'xgb': XGBClassifier(
                n_estimators=50,
                max_depth=3,
                learning_rate=0.01,
                reg_alpha=1.0,
                reg_lambda=1.0,
                random_state=42,
                n_jobs=-1,
                scale_pos_weight=weights[2]/weights[0] if len(weights) > 2 else 1
            ),
            'lr': LogisticRegression(
                C=0.1,
                max_iter=1000,
                random_state=42,
                n_jobs=-1,
                class_weight=self.class_weights
            )
        }
        
        # Entrenar modelos
        for name, model in models.items():
            try:
                model.fit(X_train, y_train)
                self.models[name] = model
                logger.log(f"✅ Modelo {name} entrenado")
            except Exception as e:
                logger.log(f"❌ Error entrenando {name}: {str(e)}", "ERROR")
        
        # Ensemble voting
        if len(self.models) > 0:
            self.ensemble = VotingClassifier(
                estimators=list(self.models.items()),
                voting='soft'
            )
            self.ensemble.fit(X_train, y_train)
            logger.log("✅ Ensemble creado")
    
    def generate_signals(self, df):
        """Genera señales con validación de diversidad"""
        if not self.models:
            logger.log("⚠️ No hay modelos entrenados", "WARNING")
            return pd.DataFrame()
        
        X, _ = self.prepare_data(df)
        if X is None:
            return pd.DataFrame()
        
        # Predicciones del ensemble
        try:
            predictions = self.ensemble.predict(X)
            probabilities = self.ensemble.predict_proba(X)
            
            # Calcular confianza
            confidence = np.max(probabilities, axis=1)
            
            # Filtrar señales de baja confianza
            filtered_predictions = np.where(confidence >= MIN_CONFIDENCE, predictions, 1)
            
            # Mapear predicciones: 0=SHORT, 1=NEUTRAL, 2=LONG
            signal_map = {0: -1, 1: 0, 2: 1}
            mapped_signals = np.array([signal_map.get(int(p), 0) for p in filtered_predictions])
            
            # Añadir ATR para cálculo de TP/SL
            atr_values = df['atr'].iloc[-len(predictions):].values if 'atr' in df.columns else np.full(len(predictions), df['close'].mean() * 0.005)
            
            # Crear DataFrame de señales
            signals = pd.DataFrame({
                'timestamp': df.index[-len(predictions):],
                'signal': mapped_signals,
                'confidence': confidence,
                'price': df['close'].iloc[-len(predictions):].values,
                'atr': atr_values
            })
            
            # Solo señales no neutrales
            signals = signals[signals['signal'] != 0]
            
            # VALIDACIÓN DE DIVERSIDAD DE SEÑALES
            if not signals.empty:
                unique_signals = signals['signal'].value_counts()
                if len(unique_signals) == 1:
                    logger.log("⚠️ Modelo generando señales uniformes - revisar entrenamiento", "WARNING")
                    # Intentar ajustar confianza mínima temporalmente
                    temp_min_confidence = MIN_CONFIDENCE * 0.9
                    signals = signals[signals['confidence'] >= temp_min_confidence]
                    logger.log(f"🔧 Ajustando confianza mínima a {temp_min_confidence:.2%}")
            
            # Log de debug mejorado
            total_predictions = len(predictions)
            unique_preds, counts = np.unique(predictions, return_counts=True)
            logger.log(f"📊 Total predicciones: {total_predictions}")
            logger.log(f"🎯 Predicciones por tipo:")
            for pred, count in zip(unique_preds, counts):
                pred_type = {0: "SHORT", 1: "NEUTRAL", 2: "LONG"}.get(pred, "UNKNOWN")
                logger.log(f"  • {pred_type}: {count} ({count/total_predictions*100:.1f}%)")
            
            # Mostrar diversidad de confianza
            if len(signals) > 0:
                logger.log(f"📈 Rango de confianza: {signals['confidence'].min():.2%} - {signals['confidence'].max():.2%}")
            
            logger.log(f"\n✅ Señales filtradas (confianza>{MIN_CONFIDENCE:.0%}): {len(signals)}")
            if len(signals) > 0:
                logger.log(f"🔥 ¡SEÑALES ACTIVAS DETECTADAS! El bot ejecutará trades automáticamente.")
            
            return signals
            
        except Exception as e:
            logger.log(f"❌ Error generando señales: {str(e)}", "ERROR")
            return pd.DataFrame()

class RiskManager:
    """Gestión de riesgo profesional"""
    
    def __init__(self, initial_capital=INITIAL_CAPITAL):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = []
        self.daily_pnl = 0
        self.trade_history = []
        
    def calculate_position_size(self, signal_confidence):
        """Calcula tamaño de posición basado en Kelly Criterion modificado"""
        # Kelly conservador
        kelly_fraction = (signal_confidence - 0.5) / 0.5
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Máximo 25% Kelly
        
        # Ajustar por capital actual
        position_size = self.current_capital * kelly_fraction * MAX_RISK_PER_TRADE

        # Establecer un tamaño mínimo de posición si la señal es válida
        min_position = max(50, self.current_capital * 0.005)  # $50 o 0.5% del capital
        if position_size < min_position and signal_confidence >= MIN_CONFIDENCE:
            position_size = min_position

        # Límites de seguridad
        max_position = self.current_capital * 0.1  # Máximo 10% del capital
        position_size = min(position_size, max_position)
        
        return position_size
    
    def calculate_stop_loss(self, entry_price, atr, direction):
        """Stop loss dinámico basado en ATR"""
        atr_multiplier = 1.5  # Conservador
        
        if direction == 1:  # LONG
            stop_loss = entry_price - (atr * atr_multiplier)
        else:  # SHORT
            stop_loss = entry_price + (atr * atr_multiplier)
            
        return stop_loss
    
    def calculate_take_profit(self, entry_price, atr, direction, risk_reward=1.5):
        """Take profit con ratio riesgo/beneficio favorable"""
        tp_distance = atr * risk_reward
        
        if direction == 1:  # LONG
            take_profit = entry_price + tp_distance
        else:  # SHORT
            take_profit = entry_price - tp_distance
            
        return take_profit
    
    def check_daily_loss_limit(self):
        """Verifica límite de pérdida diaria"""
        daily_loss_pct = abs(self.daily_pnl / self.initial_capital)
        
        if daily_loss_pct >= MAX_DAILY_LOSS:
            logger.log(f"🛑 Límite diario alcanzado: {daily_loss_pct:.2%}", "WARNING")
            return True
        return False
    
    def update_capital(self, pnl):
        """Actualiza capital y estadísticas"""
        self.current_capital += pnl
        self.daily_pnl += pnl
        
        # Log estado
        logger.log(f"💰 Capital: ${self.current_capital:.2f} | PnL Diario: ${self.daily_pnl:.2f}")

class SimulatedTrader:
    """Trading en modo simulación"""
    
    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()
        self.active_positions = []
        
    def train_system(self, df):
        """Entrena el sistema con datos históricos"""
        # Crear features
        df_features = self.feature_engineer.create_features(df.copy())
        
        # Preparar datos
        X, y = self.signal_generator.prepare_data(df_features)
        
        if X is not None and len(X) > 100:
            # Split 80/20
            split_idx = int(0.8 * len(X))
            X_train, y_train = X[:split_idx], y[:split_idx]
            
            # Escalar datos
            X_train_scaled = self.signal_generator.scaler.fit_transform(X_train)
            
            # Entrenar modelos
            self.signal_generator.train_models(X_train_scaled, y_train)
            
            logger.log("✅ Sistema entrenado y listo")
            logger.log(f"📊 Modelos activos: {list(self.signal_generator.models.keys())}")
            return True
        
        return False
    
    def execute_trade(self, signal, current_df, current_price=None):
        """Ejecuta trade en simulación con información completa"""
        # Calcular parámetros
        position_size = self.risk_manager.calculate_position_size(signal['confidence'])

        # Obtener ATR actual del DataFrame
        if 'atr' in current_df.columns and not current_df['atr'].empty:
            current_atr = current_df['atr'].iloc[-1]
            if pd.isna(current_atr) or current_atr <= 0:
                current_atr = current_df['close'].iloc[-1] * 0.005
        else:
            current_atr = current_df['close'].iloc[-1] * 0.005

        # Usar el precio actual real para la entrada
        entry_price = current_price if current_price is not None else current_df['close'].iloc[-1]

        stop_loss = self.risk_manager.calculate_stop_loss(
            entry_price, current_atr, signal['signal']
        )

        take_profit = self.risk_manager.calculate_take_profit(
            entry_price, current_atr, signal['signal']
        )

        trade = {
            'timestamp': signal['timestamp'],
            'type': 'LONG' if signal['signal'] == 1 else 'SHORT',
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': position_size,
            'confidence': signal['confidence'],
            'status': 'OPEN',
            'entry_time': datetime.datetime.now()
        }

        self.active_positions.append(trade)

        # Log detallado
        logger.log(f"\n{'='*60}")
        logger.log(f"🎯 NUEVA POSICIÓN ABIERTA:")
        logger.log(f"├─ Tipo: {'🟢 LONG' if trade['type'] == 'LONG' else '🔴 SHORT'}")
        logger.log(f"├─ Entrada: ${trade['entry_price']:.2f}")
        logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-trade['entry_price'])/trade['entry_price']*100:.2f}%)")
        logger.log(f"├─ Tamaño: ${position_size:.2f}")
        logger.log(f"├─ Confianza: {trade['confidence']:.2%}")
        logger.log(f"└─ Risk/Reward: 1:{abs(take_profit-trade['entry_price'])/abs(stop_loss-trade['entry_price']):.1f}")
        logger.log(f"{'='*60}\n")

        return trade
    
    def update_positions(self, current_price):
        """Actualiza posiciones abiertas con alertas"""
        for position in self.active_positions:
            if position['status'] != 'OPEN':
                continue
            
            # Calcular distancia a TP/SL
            if position['type'] == 'LONG':
                dist_to_sl = (current_price - position['stop_loss']) / position['stop_loss'] * 100
                dist_to_tp = (position['take_profit'] - current_price) / current_price * 100
                
                # Alertas de proximidad
                if dist_to_sl < 0.5 and dist_to_sl > 0:
                    logger.log(f"⚠️ ALERTA: LONG cerca de SL! Distancia: {dist_to_sl:.2f}%")
                elif dist_to_tp < 0.5 and dist_to_tp > 0:
                    logger.log(f"🎯 ALERTA: LONG cerca de TP! Distancia: {dist_to_tp:.2f}%")
                
                # Check stop loss y take profit
                if current_price <= position['stop_loss']:
                    self.close_position(position, current_price, 'STOP_LOSS')
                elif current_price >= position['take_profit']:
                    self.close_position(position, current_price, 'TAKE_PROFIT')
                    
            else:  # SHORT
                dist_to_sl = (position['stop_loss'] - current_price) / current_price * 100
                dist_to_tp = (current_price - position['take_profit']) / position['take_profit'] * 100
                
                # Alertas de proximidad
                if dist_to_sl < 0.5 and dist_to_sl > 0:
                    logger.log(f"⚠️ ALERTA: SHORT cerca de SL! Distancia: {dist_to_sl:.2f}%")
                elif dist_to_tp < 0.5 and dist_to_tp > 0:
                    logger.log(f"🎯 ALERTA: SHORT cerca de TP! Distancia: {dist_to_tp:.2f}%")
                
                # Check stop loss y take profit
                if current_price >= position['stop_loss']:
                    self.close_position(position, current_price, 'STOP_LOSS')
                elif current_price <= position['take_profit']:
                    self.close_position(position, current_price, 'TAKE_PROFIT')
    
    def close_position(self, position, exit_price, reason):
        """Cierra posición y calcula PnL con detalles"""
        if position['type'] == 'LONG':
            pnl = (exit_price - position['entry_price']) / position['entry_price'] * position['position_size']
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
        else:  # SHORT
            pnl = (position['entry_price'] - exit_price) / position['entry_price'] * position['position_size']
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100
        
        position['exit_price'] = exit_price
        position['pnl'] = pnl
        position['pnl_pct'] = pnl_pct
        position['status'] = 'CLOSED'
        position['exit_reason'] = reason
        position['exit_time'] = datetime.datetime.now()
        position['duration'] = (position['exit_time'] - position['entry_time']).seconds // 60
        
        # Actualizar capital
        self.risk_manager.update_capital(pnl)
        
        # Log detallado del resultado
        emoji = "💰" if pnl > 0 else "📉"
        logger.log(f"\n{emoji} POSICIÓN CERRADA:")
        logger.log(f"├─ Tipo: {position['type']}")
        logger.log(f"├─ Entrada: ${position['entry_price']:.2f}")
        logger.log(f"├─ Salida: ${exit_price:.2f}")
        logger.log(f"├─ Razón: {reason}")
        logger.log(f"├─ PnL: ${pnl:.2f} ({pnl_pct:.2f}%)")
        logger.log(f"├─ Duración: {position['duration']} minutos")
        logger.log(f"└─ Capital actualizado: ${self.risk_manager.current_capital:.2f}")
        
        # Si es el primer trade cerrado, mostrar información adicional
        closed_count = len([p for p in self.active_positions if p['status'] == 'CLOSED'])
        if closed_count == 1:
            logger.log(f"\n🎉 ¡PRIMER TRADE COMPLETADO!")
            logger.log(f"💡 El sistema está funcionando correctamente.")
            logger.log(f"📊 Continúa monitoreando para ver más trades...")
    
    def generate_report(self):
        """Genera reporte de rendimiento"""
        closed_positions = [p for p in self.active_positions if p['status'] == 'CLOSED']
        
        # Calcular métricas básicas
        current_capital = self.risk_manager.current_capital
        initial_capital = self.risk_manager.initial_capital
        roi = (current_capital - initial_capital) / initial_capital * 100
        
        if not closed_positions:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_pnl': 0,
                'sharpe_ratio': 0,
                'current_capital': current_capital,
                'roi': roi
            }
        
        # Calcular métricas
        wins = [p for p in closed_positions if p['pnl'] > 0]
        losses = [p for p in closed_positions if p['pnl'] < 0]
        
        win_rate = len(wins) / len(closed_positions) * 100
        
        total_wins = sum(p['pnl'] for p in wins) if wins else 0
        total_losses = abs(sum(p['pnl'] for p in losses)) if losses else 1
        
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        # Calcular Sharpe Ratio
        returns = [p['pnl'] / p['position_size'] for p in closed_positions]
        if len(returns) > 1:
            sharpe = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        else:
            sharpe = 0
        
        report = {
            'total_trades': len(closed_positions),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_pnl': sum(p['pnl'] for p in closed_positions),
            'sharpe_ratio': sharpe,
            'current_capital': current_capital,
            'roi': roi
        }
        
        return report

class LiveTradingBot:
    """Bot principal para trading en vivo (simulado)"""
    
    def __init__(self):
        self.trader = SimulatedTrader()
        self.is_trained = False
        self.last_signal_time = None
        self.timeframes = ['15m', '1h']
        self.total_trades_executed = 0
        
    def show_market_summary(self, df):
        """Muestra resumen rápido del mercado"""
        current_price = df['close'].iloc[-1]
        price_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
        rsi = talib.RSI(df['close'])[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volume_ratio = df['volume'].iloc[-1] / volume_avg
        
        # Obtener símbolo del activo
        asset_symbol = SYMBOL.split('/')[0]
        
        logger.log(f"\n💹 MERCADO {asset_symbol}: ${current_price:.2f} ({price_change:+.2f}%) | "
                  f"RSI: {rsi:.0f} {'🔴' if rsi > 70 else '🟢' if rsi < 30 else '⚪'} | "
                  f"Vol: {'🔥' if volume_ratio > 1.5 else '📊'} x{volume_ratio:.1f}")
    
    def initialize(self):
        """Inicializa el bot con datos históricos"""
        logger.log("🚀 Inicializando Trading Bot Profesional v2.0 MEJORADO")
        logger.log(f"💱 Activo seleccionado: {SYMBOL}")
        logger.log("📚 Descargando datos históricos...")
        
        # Obtener datos históricos para entrenamiento
        df = self.trader.data_manager.get_live_data(timeframe='15m', limit=1000)
        
        if not df.empty:
            logger.log("🤖 Entrenando modelos de machine learning con balance de clases...")
            self.is_trained = self.trader.train_system(df)
            
            if self.is_trained:
                logger.log("✅ Bot inicializado correctamente")
                logger.log(f"📊 Sistema listo con {len(self.trader.signal_generator.models)} modelos activos")
                logger.log("✨ El sistema está listo para operar en modo simulación real")
                logger.log("🔧 Mejoras implementadas: Balance de clases, umbrales sensibles, nuevos indicadores")
                self.show_next_predictions(df)
            else:
                logger.log("❌ Error en inicialización", "ERROR")
        else:
            logger.log("❌ No se pudieron obtener datos", "ERROR")
    
    def show_next_predictions(self, df):
        """Muestra próximas predicciones con niveles de TP/SL"""
        logger.log("\n" + "="*60)
        logger.log("🔮 PRÓXIMAS PREDICCIONES")
        logger.log("="*60)
        
        # Crear features
        df_features = self.trader.feature_engineer.create_features(df.copy())
        
        # Generar señales
        signals = self.trader.signal_generator.generate_signals(df_features)
        
        if not signals.empty:
            # Mostrar últimas 5 señales con TP/SL
            logger.log("\n📊 SEÑALES DE TRADING:")
            for _, signal in signals.tail(5).iterrows():
                direction = "🟢 LONG" if signal['signal'] == 1 else "🔴 SHORT"
                
                # Calcular TP/SL
                atr = signal['atr'] if signal['atr'] > 0 else signal['price'] * 0.005
                if signal['signal'] == 1:  # LONG
                    stop_loss = signal['price'] - (atr * 1.5)
                    take_profit = signal['price'] + (atr * 2.25)
                else:  # SHORT
                    stop_loss = signal['price'] + (atr * 1.5)
                    take_profit = signal['price'] - (atr * 2.25)
                
                logger.log(f"\n{direction} @ ${signal['price']:.2f}")
                logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-signal['price'])/signal['price']*100:.2f}%)")
                logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-signal['price'])/signal['price']*100:.2f}%)")
                logger.log(f"├─ Confianza: {signal['confidence']:.2%}")
                logger.log(f"└─ Hora: {signal['timestamp']}")
        else:
            logger.log("❌ No hay señales de alta confianza en este momento")
        
        # Mostrar estado del mercado detallado
        current_price = df['close'].iloc[-1]
        rsi = talib.RSI(df['close'])[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volatility = df['close'].pct_change().rolling(20).std().iloc[-1] * 100
        
        # Nuevos indicadores
        adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
        cci = talib.CCI(df['high'], df['low'], df['close'])[-1]
        mfi = talib.MFI(df['high'], df['low'], df['close'], df['volume'])[-1]
        
        trend = "ALCISTA 📈" if df['close'].iloc[-1] > df['close'].iloc[-20] else "BAJISTA 📉"
        momentum = "FUERTE" if abs(df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5] > 0.01 else "DÉBIL"
        
        asset_symbol = SYMBOL.split('/')[0]
        
        logger.log(f"\n📊 ESTADO DEL MERCADO {asset_symbol}:")
        logger.log(f"├─ Precio actual: ${current_price:.2f}")
        logger.log(f"├─ RSI: {rsi:.2f} {'(Sobrecompra)' if rsi > 70 else '(Sobreventa)' if rsi < 30 else '(Neutral)'}")
        logger.log(f"├─ ADX: {adx:.2f} {'(Tendencia fuerte)' if adx > 25 else '(Tendencia débil)'}")
        logger.log(f"├─ CCI: {cci:.2f}")
        logger.log(f"├─ MFI: {mfi:.2f}")
        logger.log(f"├─ SMA 20: ${sma_20:.2f} {'↑' if current_price > sma_20 else '↓'}")
        logger.log(f"├─ SMA 50: ${sma_50:.2f} {'↑' if current_price > sma_50 else '↓'}")
        logger.log(f"├─ Tendencia: {trend} ({momentum})")
        logger.log(f"├─ Volatilidad: {volatility:.2f}% {'🔥 ALTA' if volatility > 2 else '✅ NORMAL'}")
        logger.log(f"└─ Volumen: {'📊 Alto' if df['volume'].iloc[-1] > volume_avg * 1.5 else '📉 Normal'}")
    
    def run_live_simulation(self):
        """Ejecuta bot en modo simulación continua"""
        if not self.is_trained:
            logger.log("❌ Bot no está entrenado", "ERROR")
            logger.log("💡 Por favor, seleccione opción 1 para inicializar el sistema primero")
            return
        
        asset_symbol = SYMBOL.split('/')[0]
        
        logger.log(f"\n🤖 INICIANDO TRADING EN MODO SIMULACIÓN - {asset_symbol}")
        logger.log(f"💰 Capital inicial: ${self.trader.risk_manager.initial_capital}")
        logger.log(f"📍 Posiciones máximas: {MAX_POSITIONS}")
        logger.log("\n📌 El bot ejecutará trades INMEDIATAMENTE cuando detecte señales")
        logger.log("📌 Mostrará predicciones con niveles de TP/SL")
        logger.log("📌 Actualizará posiciones en tiempo real")
        logger.log("📌 Sistema mejorado con balance de clases y nuevos indicadores")
        logger.log(f"📌 Confianza mínima para trades: {MIN_CONFIDENCE:.0%}\n")
        
        cycle = 0
        while True:
            try:
                cycle += 1
                logger.log(f"\n{'='*60}")
                logger.log(f"📍 Ciclo #{cycle} - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Verificar límite diario
                if self.trader.risk_manager.check_daily_loss_limit():
                    logger.log("🛑 Límite diario alcanzado - Trading pausado")
                    time.sleep(3600)
                    continue
                
                # Obtener datos actuales
                df = self.trader.data_manager.get_live_data(timeframe='15m', limit=200)
                
                if df.empty:
                    logger.log("⚠️ No hay datos disponibles", "WARNING")
                    time.sleep(60)
                    continue
                
                # Precio actual
                current_price = df['close'].iloc[-1]
                
                # Mostrar resumen de mercado
                if cycle <= 10 or cycle % 3 == 0:
                    self.show_market_summary(df)
                
                # Mostrar información del ciclo
                if cycle == 1:
                    logger.log(f"├─ Sistema: ✅ ACTIVO")
                    logger.log(f"├─ Modelos ML: {len(self.trader.signal_generator.models)} activos")
                    logger.log(f"├─ Confianza mínima: {MIN_CONFIDENCE:.0%}")
                logger.log(f"├─ Precio {asset_symbol}: ${current_price:.2f}")
                logger.log(f"├─ Posiciones abiertas: {len([p for p in self.trader.active_positions if p['status'] == 'OPEN'])}/{MAX_POSITIONS}")
                logger.log(f"├─ Trades ejecutados: {self.total_trades_executed}")
                logger.log(f"└─ Capital: ${self.trader.risk_manager.current_capital:.2f}")
                
                # Actualizar posiciones con precio actual
                self.trader.update_positions(current_price)
                
                # Generar nuevas señales desde ciclo 2
                if cycle >= 2:
                    logger.log(f"\n🔍 [Ciclo {cycle}] Analizando mercado para oportunidades...")
                    df_features = self.trader.feature_engineer.create_features(df.copy())
                    
                    # Mostrar algunos indicadores clave
                    try:
                        current_rsi = talib.RSI(df['close'])[-1]
                        current_macd = talib.MACD(df['close'])[0][-1]
                        current_adx = talib.ADX(df['high'], df['low'], df['close'])[-1]
                        logger.log(f"📊 Indicadores: RSI={current_rsi:.1f}, MACD={current_macd:.1f}, ADX={current_adx:.1f}")
                    except:
                        pass
                    
                    signals = self.trader.signal_generator.generate_signals(df_features)
                    
                    if not signals.empty:
                        logger.log(f"\n🎯 ¡{len(signals)} SEÑALES DETECTADAS!")
                        
                        # Contar tipos de señales
                        long_signals = len(signals[signals['signal'] == 1])
                        short_signals = len(signals[signals['signal'] == -1])
                        
                        if long_signals > 0:
                            logger.log(f"🟢 LONG: {long_signals} señales")
                        if short_signals > 0:
                            logger.log(f"🔴 SHORT: {short_signals} señales")
                        
                        # Mostrar mejor señal
                        best_signal = signals.loc[signals['confidence'].idxmax()]
                        logger.log(f"🏆 Mejor señal: {'LONG' if best_signal['signal'] == 1 else 'SHORT'} con {best_signal['confidence']:.1%} confianza")
                        
                        # Verificar posiciones abiertas
                        open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                        available_slots = MAX_POSITIONS - len(open_positions)
                        
                        # Mostrar posiciones abiertas actual
                        logger.log(f"📊 Posiciones abiertas actuales: {len(open_positions)}")
                        
                        if available_slots > 0:
                            logger.log(f"💼 Espacios disponibles para trades: {available_slots}")
                            
                            # Tomar las mejores señales por confianza
                            signals_sorted = signals.sort_values('confidence', ascending=False)
                            signals_to_process = signals_sorted.head(available_slots)
                            
                            for idx, (_, signal) in enumerate(signals_to_process.iterrows()):
                                logger.log(f"\n🔄 Procesando señal {idx+1}/{len(signals_to_process)}:")
                                logger.log(f"   Tipo: {'🟢 LONG' if signal['signal'] == 1 else '🔴 SHORT'}")
                                logger.log(f"   Precio: ${signal['price']:.2f}")
                                logger.log(f"   Confianza: {signal['confidence']:.2%}")
                                
                                # Ejecutar directamente
                                logger.log("   ✅ EJECUTANDO TRADE")
                                try:
                                    self.trader.execute_trade(signal, df, current_price)
                                    self.total_trades_executed += 1
                                    logger.log("   ✅ Trade ejecutado exitosamente")
                                except Exception as e:
                                    logger.log(f"   ❌ Error ejecutando trade: {str(e)}", "ERROR")
                                
                                # Pequeña pausa para ver la acción
                                time.sleep(1)
                        else:
                            logger.log(f"⚠️ Máximo de posiciones alcanzado ({MAX_POSITIONS}) - esperando cierre de posiciones")
                    else:
                        logger.log(f"❌ No hay señales con confianza suficiente (>{MIN_CONFIDENCE:.0%})")
                        # Mostrar info de debug
                        X, _ = self.trader.signal_generator.prepare_data(df_features)
                        if X is not None and len(X) > 0:
                            logger.log(f"🔍 Sistema analizando {len(X)} patrones de mercado...")
                
                # Mostrar estado de posiciones abiertas
                open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                if open_positions and cycle % 3 == 0:
                    logger.log("\n📋 POSICIONES ABIERTAS:")
                    for i, pos in enumerate(open_positions):
                        pnl_actual = ((current_price - pos['entry_price']) / pos['entry_price'] * 100 
                                     if pos['type'] == 'LONG' 
                                     else (pos['entry_price'] - current_price) / pos['entry_price'] * 100)
                        
                        # Emoji según PnL
                        pnl_emoji = "🟢" if pnl_actual > 0 else "🔴" if pnl_actual < 0 else "⚪"
                        
                        logger.log(f"{i+1}. {pos['type']} @ ${pos['entry_price']:.2f} | "
                                  f"PnL: {pnl_emoji} {pnl_actual:+.2f}% | "
                                  f"Tiempo: {(datetime.datetime.now() - pos['entry_time']).seconds // 60} min")
                
                # Mostrar estado cada 5 ciclos
                if cycle % 5 == 0:
                    report = self.trader.generate_report()
                    logger.log(f"\n📊 REPORTE DE RENDIMIENTO:")
                    logger.log(f"├─ Trades totales: {report['total_trades']}")
                    logger.log(f"├─ Win Rate: {report['win_rate']:.1f}%")
                    logger.log(f"├─ Profit Factor: {report['profit_factor']:.2f}")
                    logger.log(f"├─ PnL Total: ${report['total_pnl']:.2f}")
                    logger.log(f"├─ ROI: {report.get('roi', 0):.2f}%")
                    logger.log(f"└─ Capital actual: ${report['current_capital']:.2f}")
                    
                    # Solo mostrar predicciones cada 10 ciclos
                    if cycle % 10 == 0:
                        self.show_next_predictions(df)
                
                # Esperar antes del próximo ciclo
                time.sleep(30)  # 30 segundos
                
            except KeyboardInterrupt:
                logger.log("\n🛑 Trading detenido por el usuario")
                break
            except Exception as e:
                logger.log(f"❌ Error en ciclo: {str(e)}", "ERROR")
                time.sleep(60)
        
        # Reporte final
        logger.log("\n" + "="*60)
        logger.log("📊 REPORTE FINAL")
        logger.log("="*60)
        
        final_report = self.trader.generate_report()
        for key, value in final_report.items():
            if isinstance(value, float):
                logger.log(f"{key}: {value:.2f}")
            else:
                logger.log(f"{key}: {value}")

def main():
    """Función principal"""
    asset_symbol = SYMBOL.split('/')[0]
    
    print("\n" + "="*60)
    print(f"🚀 {asset_symbol} TRADING BOT PROFESIONAL v2.0 MEJORADO")
    print("\n⚡ IMPORTANTE:")
    print("1. Primero DEBES inicializar el sistema (opción 1)")
    print("2. El bot detecta señales con ML mejorado y balance de clases")
    print("3. Sistema configurado para 1 posición máxima")
    print("4. Los resultados son 100% simulados - NO es dinero real")
    print("="*60)
    print("✅ Sistema mejorado con:")
    print("  • Balance de clases en modelos ML")
    print("  • Umbrales más sensibles")
    print("  • Nuevos indicadores técnicos (ADX, CCI, MFI)")
    print("  • Validación de diversidad de señales")
    print("  • Position sizing dinámico")
    print("  • Límites de pérdida diaria")
    print("="*60)
    
    bot = LiveTradingBot()
    
    # Menú principal
    while True:
        print("\nSeleccione opción:")
        print("1. Inicializar/Reinicializar sistema")
        print("2. Ejecutar simulación en vivo ⭐ (RECOMENDADO)")
        print("3. Ver predicciones actuales")
        print("4. Salir")
        
        opcion = input("\nOpción (1-4): ")
        
        if opcion == '1':
            print("\n🔄 Inicializando sistema...")
            print("⏳ Esto puede tomar 30-60 segundos...")
            bot.initialize()
            print("\n✅ Sistema listo para trading!")
            print("💡 Ahora seleccione opción 2 para comenzar la simulación")
        elif opcion == '2':
            if bot.is_trained:
                bot.run_live_simulation()
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de ejecutar la simulación")
        elif opcion == '3':
            if bot.is_trained:
                df = bot.trader.data_manager.get_live_data()
                bot.show_next_predictions(df)
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
        elif opcion == '4':
            print(f"👋 Gracias por usar {asset_symbol} Trading Bot Pro v2.0")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()