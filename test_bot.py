# Test simplificado del bot para verificar funcionamiento
import sys
import os

print("="*70)
print("🚀 BTC TRADING BOT PROFESIONAL v3.0 - TEST")
print("="*70)

# Test de imports
print("Probando imports...")

try:
    import pandas as pd
    print("✅ pandas OK")
except ImportError as e:
    print(f"❌ pandas ERROR: {e}")
    sys.exit(1)

try:
    import numpy as np
    print("✅ numpy OK")
except ImportError as e:
    print(f"❌ numpy ERROR: {e}")
    sys.exit(1)

try:
    import ccxt
    print("✅ ccxt OK")
except ImportError as e:
    print(f"❌ ccxt ERROR: {e}")
    sys.exit(1)

try:
    import talib
    print("✅ talib OK")
except ImportError as e:
    print(f"❌ talib ERROR: {e}")
    sys.exit(1)

try:
    from sklearn.ensemble import RandomForestClassifier
    print("✅ sklearn OK")
except ImportError as e:
    print(f"❌ sklearn ERROR: {e}")
    sys.exit(1)

try:
    import xgboost
    print("✅ xgboost OK")
except ImportError as e:
    print(f"❌ xgboost ERROR: {e}")
    sys.exit(1)

try:
    import lightgbm
    print("✅ lightgbm OK")
except ImportError as e:
    print(f"❌ lightgbm ERROR: {e}")
    sys.exit(1)

print("\n🎯 TODAS LAS DEPENDENCIAS ESTÁN INSTALADAS CORRECTAMENTE!")
print("\n📊 Probando funcionalidad básica...")

# Test básico de datos
try:
    # Crear datos simulados
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1H')
    np.random.seed(42)
    prices = 50000 + np.cumsum(np.random.randn(100) * 100)
    
    df = pd.DataFrame({
        'timestamp': dates,
        'close': prices,
        'high': prices * 1.01,
        'low': prices * 0.99,
        'open': np.roll(prices, 1),
        'volume': np.random.lognormal(10, 0.5, 100)
    })
    
    print(f"✅ Datos creados: {len(df)} filas")
    
    # Test de indicadores técnicos
    df['rsi'] = talib.RSI(df['close'].values)
    df['sma_20'] = talib.SMA(df['close'].values, timeperiod=20)
    
    print("✅ Indicadores técnicos calculados")
    
    # Test de modelo ML básico
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    
    # Crear features simples
    df['returns'] = df['close'].pct_change()
    df['target'] = (df['returns'].shift(-1) > 0).astype(int)
    
    # Preparar datos
    features = ['rsi', 'sma_20']
    df_clean = df.dropna()
    
    if len(df_clean) > 50:
        X = df_clean[features].values
        y = df_clean['target'].values
        
        # Entrenar modelo
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        model.fit(X_scaled[:-10], y[:-10])
        
        # Hacer predicción
        predictions = model.predict(X_scaled[-10:])
        probabilities = model.predict_proba(X_scaled[-10:])
        
        print(f"✅ Modelo entrenado y predicciones generadas")
        print(f"📊 Últimas 5 predicciones: {predictions[-5:]}")
        print(f"📊 Confianza promedio: {np.max(probabilities, axis=1).mean():.2%}")
    
    print("\n🎉 ¡TODAS LAS PRUEBAS PASARON EXITOSAMENTE!")
    print("🚀 El sistema está listo para ejecutar el bot completo")
    
except Exception as e:
    print(f"❌ Error en las pruebas: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n" + "="*70)
print("Test completado. Presiona Enter para continuar...")
input()
