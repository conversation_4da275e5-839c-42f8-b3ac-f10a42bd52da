#!/usr/bin/env python3
# Script para encontrar el error en btc9.py

with open('btc9.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

print("Buscando llamadas a execute_trade...")
for i, line in enumerate(lines):
    if 'execute_trade(' in line:
        print(f"Línea {i+1}: {line.strip()}")
        # Mostrar contexto
        start = max(0, i-3)
        end = min(len(lines), i+4)
        print("Contexto:")
        for j in range(start, end):
            marker = ">>> " if j == i else "    "
            print(f"{marker}{j+1}: {lines[j].rstrip()}")
        print("-" * 50)

print("\nBuscando mensajes específicos...")
for i, line in enumerate(lines):
    if '🔥' in line or 'SEÑALES ACTIVAS' in line or 'alta confianza' in line:
        print(f"Línea {i+1}: {line.strip()}")
        # Mostrar contexto
        start = max(0, i-5)
        end = min(len(lines), i+6)
        print("Contexto:")
        for j in range(start, end):
            marker = ">>> " if j == i else "    "
            print(f"{marker}{j+1}: {lines[j].rstrip()}")
        print("-" * 50)
