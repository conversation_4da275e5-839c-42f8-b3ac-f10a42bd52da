print("Python funciona correctamente")
print("Probando imports...")

try:
    import pandas as pd
    print("✅ pandas OK")
except ImportError as e:
    print(f"❌ pandas ERROR: {e}")

try:
    import numpy as np
    print("✅ numpy OK")
except ImportError as e:
    print(f"❌ numpy ERROR: {e}")

try:
    import ccxt
    print("✅ ccxt OK")
except ImportError as e:
    print(f"❌ ccxt ERROR: {e}")

try:
    import talib
    print("✅ talib OK")
except ImportError as e:
    print(f"❌ talib ERROR: {e}")

try:
    import sklearn
    print("✅ sklearn OK")
except ImportError as e:
    print(f"❌ sklearn ERROR: {e}")

try:
    import xgboost
    print("✅ xgboost OK")
except ImportError as e:
    print(f"❌ xgboost ERROR: {e}")

try:
    import lightgbm
    print("✅ lightgbm OK")
except ImportError as e:
    print(f"❌ lightgbm ERROR: {e}")

print("Test completado")
