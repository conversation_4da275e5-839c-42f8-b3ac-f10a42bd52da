# 🚀 BTC Trading Bot Profesional v3.0

## 🎯 Mejoras Implementadas

### ✅ **UN TRADE POR VEZ**
- Sistema rediseñado para ejecutar solo una posición a la vez
- Máxima precisión y control de riesgo
- Cola de señales priorizadas por confianza

### ✅ **Detección Precisa de TP/SL**
- Tolerancia configurable para detección exacta
- Alertas de proximidad mejoradas
- Confirmación de precios con múltiples validaciones

### ✅ **Trailing Stop Loss**
- Activación automática cuando el trade está en ganancia
- Ajuste dinámico basado en ATR
- Protección de ganancias mejorada

### ✅ **Modelos de ML Avanzados**
- 5 algoritmos optimizados: RandomForest, XGBoost, LightGBM, GradientBoosting, LogisticRegression
- Validación cruzada temporal
- Ensemble ponderado por rendimiento
- Persistencia de modelos entrenados

### ✅ **Features Técnicos Expandidos**
- 100+ indicadores técnicos
- RSI múltiples períodos
- Bollinger Bands avanzados
- Patrones de velas japonesas
- Análisis de estructura de mercado

### ✅ **Gestión de Riesgo Mejorada**
- Kelly Criterion adaptativo
- Control de drawdown
- Límites de pérdidas consecutivas
- Position sizing dinámico

## 🛠️ Instalación

```bash
# Instalar dependencias
pip install -r requirements.txt

# Ejecutar el bot
python btc9.py
```

## 📊 Configuración

### Parámetros Principales (btc9.py líneas 27-38):
```python
LOOKBACK = 50                    # Datos históricos para predicción
MAX_RISK_PER_TRADE = 0.03       # 3% riesgo por trade
MAX_DAILY_LOSS = 0.08           # 8% pérdida máxima diaria
MIN_CONFIDENCE = 0.65           # 65% confianza mínima
MAX_POSITIONS = 1               # SOLO UN TRADE POR VEZ
TRAILING_STOP_ENABLED = True    # Trailing stop automático
TP_SL_TOLERANCE = 0.0001        # Tolerancia para TP/SL (0.01%)
```

## 🎮 Uso

1. **Inicializar Sistema** (Opción 1)
   - Descarga datos históricos
   - Entrena 5 modelos de ML
   - Valida rendimiento
   - Guarda modelos entrenados

2. **Ejecutar Simulación** (Opción 2)
   - Análisis continuo del mercado
   - Detección de señales de alta confianza
   - Ejecución de UN trade por vez
   - Monitoreo en tiempo real

3. **Ver Predicciones** (Opción 3)
   - Análisis actual del mercado
   - Señales detectadas
   - Niveles de TP/SL calculados

## 📈 Características Técnicas

### Modelos de Machine Learning:
- **RandomForest**: Ensemble robusto
- **XGBoost**: Gradient boosting optimizado
- **LightGBM**: Eficiencia y precisión
- **GradientBoosting**: Boosting clásico
- **LogisticRegression**: Baseline lineal

### Indicadores Técnicos:
- RSI (7, 14, 21 períodos)
- MACD y señales
- Bollinger Bands (20, 50)
- ATR múltiples períodos
- Stochastic, Williams %R, CCI
- OBV y análisis de volumen

### Filtros de Calidad:
- Confianza mínima 65%
- Consenso entre modelos (60%+)
- Filtro de volatilidad extrema
- Validación de estructura de mercado

## ⚠️ Importante

- **MODO SIMULACIÓN**: Todos los trades son simulados
- **NO DINERO REAL**: Sistema de prueba únicamente
- **UN TRADE**: Solo una posición abierta por vez
- **TRAILING STOP**: Protección automática de ganancias

## 📊 Métricas de Rendimiento

El sistema reporta:
- Win Rate en tiempo real
- Profit Factor
- Drawdown máximo
- ROI total
- Pérdidas consecutivas
- Duración promedio de trades

## 🔧 Archivos Generados

- `modelos/`: Modelos ML entrenados
- `logs/`: Registros de actividad
- `trades/`: Historial de trades
- `reports/`: Reportes de rendimiento
